<template>
  <div>
    <div v-if="loading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg p-2 flex items-center justify-center">
            <div class="tw-loader"></div> 
            <div class="mr-3">{{ outlierLoadingMessage }}</div>
          </div>
    </div>
    <div v-else>
      <!-- Filters -->
       <div class="w-full px-4 bg-gray-100">
      <div class="max-w-7xl mx-auto bg-white shadow-sm rounded-md px-6 py-4">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Cluster<span
                class="text-red-500 ml-1">*</span></label>
            <DynamicFilter v-model="filters.cluster" :multiselect="false" label="Cluster" placeholder="Select Clusters"
              :options="clusterOptions || []" value-key="value" label-key="label"
              :searchable="(clusterOptions?.length || 0) > 10" variant="outline" @change="handelClusterChange()"
              size="sm" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Store<span class="text-red-500 ml-1">*</span></label>
            <DynamicFilter v-model="filters.storeId" :multiselect="true" label="Store" placeholder="Select Stores"
              :options="storeOptions || []" value-key="code" label-key="name"
              :searchable="(storeOptions?.length || 0) > 10" variant="outline" @change="handelStoreChange()"
              size="sm" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Group</label>
            <DynamicFilter v-model="filters.group" :multiselect="true" label="Group" placeholder="Select Group"
              :options="groupOptions || []" value-key="code" label-key="name"
              :searchable="(groupOptions?.length || 0) > 10" variant="outline" @change="handelGroupChange()"
              size="sm" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Department</label>
            <DynamicFilter v-model="filters.department" :multiselect="true" label="Department"
              placeholder="Select Department" :options="departmentOptions || []" value-key="code" label-key="name"
              @change="handelDepartmentChange()" :searchable="(departmentOptions?.length || 0) > 10" variant="outline"
              size="sm" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Class</label>
            <DynamicFilter v-model="filters.class" :multiselect="true" label="Class" placeholder="Select Class"
              :options="classOptions || []" value-key="code" label-key="name"
              :searchable="(classOptions?.length || 0) > 10" variant="outline" size="sm" />
          </div>

          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Sub Class</label>
            <DynamicFilter v-model="filters.subclass" :multiselect="true" label="SubClass"
              placeholder="Select Sub class" :options="subclassOptions || []" value-key="code" label-key="name"
              :searchable="(subclassOptions?.length || 0) > 10" variant="outline" size="sm" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700 flex items-center mb-1">
              Outlier Status
              <div class="relative inline-block ml-1">
                <!-- Info button -->
                <button type="button" class="text-gray-400 hover:text-gray-700 focus:outline-none font-bold border border-gray-300 rounded-full w-3 h-3 flex items-center justify-center text-[0.5rem] bg-white"
                  @mouseenter="showOutlierInfo = true" @mouseleave="showOutlierInfo = false"
                  @focus="showOutlierInfo = true" @blur="showOutlierInfo = false" tabindex="0">
                  i
                </button>
                <!-- Tooltip -->
                <div v-if="showOutlierInfo"
                  class="absolute left-1/2 -translate-x-1/2 mt-2 w-64 rounded-lg bg-gray-600 text-tx-primary text-sm p-3 shadow-lg z-50 pointer-events-none"
                  style="pointer-events: none;">
                  <p class="font-semibold">Major Outlier</p>
                  <p class="mb-2">LM contribution spike without matching sales (GMV/day). Likely an anomaly.</p>
                  <p class="font-semibold">Minor Outlier</p>
                  <p>Unusual LM contribution compared to similar stores/months. May not need action.</p>
                </div>
              </div>
            </label>
            <DynamicFilter v-model="filters.outlierStatus" :multiselect="false" label="Outlier"
              placeholder="Select Outlier Status" :options="outlierStatusOptions || []" value-key="code"
              label-key="name" :searchable="false" variant="outline" size="sm" />
          </div>
          <div class="flex mt-6 gap-2">
            <button @click="resetFilters"
              class="px-4 h-10 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-secondary">
              Clear All
            </button>
            <button @click="applyFilters"
              class="px-4 h-10 text-sm font-medium border border-transparent rounded-md focus:outline-none focus:ring-2 bg-tertiary hover:bg-tertiary text-tx-primary focus:ring-secondary">
              Apply Filters
            </button>
          </div>
        </div>
      </div>
      </div>
      <!-- Table -->
      <!-- Data Table -->
      <div class="overflow-x-auto px-6">
        <div class="flex justify-between items-center mt-2">
          <!-- Left Side: Visualization and Download -->
          <div class="flex gap-3">
            <button @click="exportData()"
              class="flex items-center gap-2 px-4 py-2 h-10 bg-tertiary hover:bg-tertiary text-tx-primary text-sm font-semibold rounded-lg shadow transition">
              <div v-if="isDownload" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <Download v-else class="h-4 w-4" />
            </button>

            <button @click="openChart()"
              class="flex items-center gap-2 px-4 py-2 h-10 bg-tertiary hover:bg-tertiary text-sm text-tx-primary font-semibold rounded-lg shadow transition">
              <BarChart3 class="h-4 w-4" />
              Visualization
            </button>
          </div>

          <!-- Right Side: Approve and Reject -->
          <div class="flex gap-3">
            <button :disabled="isApprove || isReject || isDisabled" @click="approveAll()"
              class="flex items-center gap-2 bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-4 py-2 rounded-md text-sm font-medium transition-colors"
              :title="isDisabled ? 'Scenario completed — actions disabled' : ''">
              <div v-if="isApprove" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              <Check class="h-6 w-4" />
              <span>Approve All</span>
            </button>

            <button :disabled="isApprove || isReject || isDisabled" @click="rejectAll()"
              class="flex items-center gap-2 bg-red-500 hover:bg-red-700 disabled:bg-gray-400 text-tx-primary px-4 py-2 rounded-md text-sm font-medium transition-colors"
              :title="isDisabled ? 'Scenario completed — actions disabled' : ''">
              <div v-if="isReject" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
              <X class="h-6 w-4" />
              <span>Reject All</span>
            </button>
          </div>
        </div>

        <!-- No Data Message -->
        <div v-if="paginatedRows.length === 0" class="text-center py-8 text-gray-500">
          No outlier data found for the selected filters
        </div>

        <!-- Table Container with Sticky Columns -->
        <div v-else class="relative" >
          <!-- Table Wrapper for Horizontal Scroll -->
          <div class="overflow-x-auto max-h-[70vh] overflow-y-auto mt-3" @scroll="debouncedScrollHandler" ref="scrollContainer" style="scroll-behavior: smooth;">
            <table class="table-auto border-separate border-[0.2px] border-spacing-0 rounded-md mb-4 w-full bg-white">
              <!-- Sticky Table Header -->
              <thead class="bg-primary font-semibold text-xs tracking-wide sticky top-0 z-20">
                <tr>
                  <th class="font-semibold sticky left-0 z-30 px-2 py-1 border  min-w-[60px] bg-primary">Cluster</th>
                  <th class="font-semibold sticky left-[60px] z-30 px-2 py-1 border  min-w-[50px] bg-primary">Store</th>
                  <th class="font-semibold sticky left-[110px] z-30 px-2 py-1 border  min-w-[70px] bg-primary">Group</th>
                  <th class="font-semibold sticky left-[180px] z-30 px-2 py-1 border  min-w-[80px] bg-primary">Department</th>
                  <th class="font-semibold sticky left-[260px] z-30 px-2 py-1 border  min-w-[120px] bg-primary">Class</th>
                  <th class="font-semibold sticky left-[380px] z-30 px-2 py-1 border  min-w-[100px] bg-primary">Subclass</th>

                  <!-- Scrollable Columns --> 
                  <th class="font-semibold px-2 py-1 border  min-w-[80px]">Month</th>
                  <th class="font-semibold px-2 py-1 border  min-w-[60px]">LM</th>
                  <th class="font-semibold px-2 py-1 border  min-w-[80px] whitespace-nowrap">LM Contribution</th>
                  <th class="font-semibold px-2 py-1 border  min-w-[100px]">{{ performanceMetric === 'GMV' ? 'GMV' : performanceMetric.charAt(0).toUpperCase() + performanceMetric.slice(1).toLowerCase() }}/Day</th>
                  <th class="font-semibold px-2 py-1 border  min-w-[80px] whitespace-nowrap">Outlier Status</th>
                  <th class="font-semibold px-2 py-1 border  min-w-[100px] whitespace-nowrap">Revised LM</th>
                  <th class="font-semibold px-2 py-1 border  min-w-[80px]">Action</th>
                </tr>

              </thead>


              <!-- Table Body -->
              <tbody>
                <tr v-for="(row, index) in paginatedRows" :key="index"
                  :class="index % 2 === 0 ? 'bg-white' : 'bg-gray-50'" class="text-xs">

                  <!-- Frozen Columns -->
                  <td class="sticky left-0 z-10 bg-white border  min-w-[60px] text-center">
                    {{ row.cluster }}
                  </td>
                  <td class="sticky left-[60px] z-10 bg-white border  min-w-[50px] text-center">
                    {{ row.storeId }}
                  </td>
                  <td class="sticky left-[110px] z-10 bg-white border  min-w-[70px] text-center">
                    {{ row.group }}
                  </td>
                  <td class="sticky left-[180px] z-10 bg-white border  min-w-[80px] text-center">
                    {{ row.department }}
                  </td>
                  <td class="sticky left-[260px] z-10 bg-white border  min-w-[120px] text-center">
                    {{ row.class }}
                  </td>
                  <td class="px-1 sticky left-[380px] z-10 bg-white border  min-w-[100px] text-center whitespace-nowrap">
                    {{ row.subclass }}
                  </td>

                  <!-- Scrollable Columns -->
                  <td class="px-2 py-1 border  min-w-[80px] text-center whitespace-nowrap">
                    {{ formatMonth(row.month) }}
                  </td>
                  <td class="px-2 py-1 border  min-w-[60px] text-center">
                    {{ row.totalLm !== null && row.totalLm !== undefined ? row.totalLm.toFixed(2) : '' }}
                  </td>
                  <td class="px-2 py-1 border  min-w-[80px] text-center">
                    {{ row.lmContrib }}
                  </td>
                  <td class="px-2 py-1 border  min-w-[100px] text-center">
                    {{ row.perDay !== null && row.perDay !== undefined ? row.perDay.toFixed(2) : '' }}
                  </td>
                  <td class="px-2 py-1 border  min-w-[80px] text-center">
                    <span :class="['px-2', getOutlierStatusClass(row.outlierStatus)]">
                      {{ getLabelByValue(row.outlierStatus) }}
                    </span>
                  </td>
                  <td class="px-2 py-1 border  min-w-[100px] text-center">
                    {{ row.suggestedTotalLm !== null && row.suggestedTotalLm !== undefined ?
                      row.suggestedTotalLm.toFixed(2) : '' }}
                  </td>
                  <td class="px-2 py-1 border  min-w-[80px] text-center">
                    <!-- Action buttons -->
                    <div v-if="row.outlierFinalStatus === 'APPROVED'">
                      <span class="bg-green-200 text-green-700 px-2 py-0.5 rounded text-xs font-semibold">Approved</span>
                    </div>
                    <div v-else-if="row.outlierFinalStatus === 'REJECTED'">
                      <span class="bg-red-100 text-red-700 px-2 py-0.5 rounded text-xs font-semibold">Rejected</span>
                    </div>
                    <div v-else-if="isOutlier(row.outlierStatus)" class="flex gap-1 justify-center">
                      <button v-if="!row.processingReject" @click="approveRow(row)"
                        :disabled="row.processingApprove || row.processingReject || isDisabled"
                        class="bg-green-200 hover:bg-green-200 disabled:bg-gray-400 text-tx-green-700 px-2 py-1 rounded text-xs transition-colors">
                        {{ row.processingApprove ? "Processing..." : "Approve" }}
                      </button>
                      <button v-if="!row.processingApprove" @click="rejectRow(row)"
                        :disabled="row.processingApprove || row.processingReject || isDisabled"
                        class="bg-red-500 hover:bg-red-600 disabled:bg-gray-400 text-tx-primary px-2 py-1 rounded text-xs transition-colors">
                        {{ row.processingReject ? "Processing..." : "Reject" }}
                      </button>
                    </div>
                    <div v-else>-</div>
                  </td>
                </tr>

              </tbody>
              
            </table>
            <div v-if="showLoader" class="flex items-center justify-center space-x-2 text-gray-500 py-4">
                <Loader class="animate-spin w-4 h-4 text-secondary" />
            </div>
            
          </div>
        </div>
      </div>


      <!-- Chart Modal -->
      <div v-if="showChartModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div class="bg-white rounded-lg px-6 py-1 max-w-4xl w-full mx-4 max-h-[99vh]" @click.stop>
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold">Outlier Chart</h2>
            <button @click="closeChart" class="text-gray-500 hover:text-gray-700 text-2xl">
              &times;
            </button>
          </div>
          <OutlierChart :originalRows="filtersData" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from "vue";
import Multiselect from "@vueform/multiselect"; // corrected import
import "@vueform/multiselect/themes/default.css"; // required for styling
import OutlierChart from "./OutlierChart.vue";
import DynamicFilter from "./common/DynamicFilter.vue";
import axios from "axios";
import { useStepsStore } from "../stores/NavigationStore";
import {setOptimizerRunStatus,getOptimizerRunStatus } from "../services/api.js"
import {Loader, Download, BarChart3, Check, X } from "lucide-vue-next";
import { ref as vueRef } from 'vue'
const showOutlierInfo = vueRef(false)
const stepsStore = useStepsStore();
const scrollContainer = ref(null)
let isFetchingNextPage = ref(false);
const showLoader = ref(false);
let outlierLoadingMessage =  ref('Loading...');
let lastScrollTop = 0

// Filters and options
const filters = ref({
  group: [],
  department: [],
  class: [],
  subclass: [],
  storeId: [],
  outlierStatus: '',
  cluster: 1,
});
const props = defineProps({
  scenarioPhase: {
    type: String,
    default: ''
  }
})
const isDisabled = computed(() => props.scenarioPhase === 'completed')

const clusterOptions = computed(() => {
  const uniqueClusters = [...new Set(filtersData.value.map(r => r.cluster_num))]
  return uniqueClusters.map(cluster => ({
    value: cluster,
    label: `${cluster}`
  }))
})

const storeOptions = computed(() => {
  // Accept 0 as a valid cluster value
  if (filters.value.cluster === null || filters.value.cluster === undefined) return []
  const filtered = filtersData.value.filter(
    store => store.cluster_num === filters.value.cluster
  )
  const uniqueStores = [
    ...new Map(filtered.map(store => [store.loc_cd, store])).values()
  ]
  return uniqueStores.map(store => ({
    value: store.loc_cd,
    label: `${store.loc_cd} - ${store.loc_nm}`
  }))
})

function debounce(fn, delay = 200) {
  let timeout;
  return function (...args) {
    if (timeout) clearTimeout(timeout);
    timeout = setTimeout(() => fn.apply(this, args), delay);
  };
}

function getFallbackStore() {
  const currentCluster = filters.value.cluster ?? 1;
  const filtered = filtersData.value.filter(
    store => store.cluster_num === currentCluster
  );
  const uniqueStores = [
    ...new Map(filtered.map(store => [store.loc_cd, store])).values()
  ];
  
  return uniqueStores.length > 0 ? uniqueStores[0].loc_cd : null;
}
const handleScroll = async () => {
  const el = scrollContainer.value;
  if (!el || loading.value) return;

  // Only proceed if vertical scroll happened
  const currentScrollTop = el.scrollTop;
  if (currentScrollTop === lastScrollTop) return;

  lastScrollTop = currentScrollTop;

  const threshold = 100;
  const isNearBottom = el.scrollTop + el.clientHeight >= el.scrollHeight - threshold;

  if (isNearBottom && currentPage.value < totalPages.value) {
    isFetchingNextPage.value = true;
    showLoader.value = true;
    currentPage.value++;
    await fetchOutlierData(true);
    isFetchingNextPage.value = false;
    showLoader.value = false;
  }
};

const debouncedScrollHandler = debounce(handleScroll, 200);

const groupOptions = computed(() => {
  const source = Array.isArray(filtersData.value) ? filtersData.value : []

  // If store is selected, filter by it
  const filtered = filters.value.storeId && filters.value.storeId.length > 0
    ? source.filter(store => filters.value.storeId.includes(store.loc_cd))
    : source

  const uniqueGroups = [...new Set(filtered.map(store => store.grp_nm))]

  return uniqueGroups
    .filter(g => g) // filter out null/undefined if any
    .map(g => ({ value: g, label: g }))
})

window.addEventListener('resize', () => {
  checkNeedForPagination();
});

const checkNeedForPagination = async () => {
  const el = scrollContainer.value;
  if (!el || loading.value || isFetchingNextPage.value) return;
  const isScrollable = el.scrollHeight > el.clientHeight;
  if (!isScrollable && currentPage.value < totalPages.value) {
    isFetchingNextPage.value = true;
    currentPage.value += 1;
    await fetchOutlierData(true); // true = append
    isFetchingNextPage.value = false;

    // Recheck after DOM updates
    await nextTick();
    checkNeedForPagination();
  }
};

const departmentOptions = computed(() => {
  let filtered = filtersData.value
  if (filters.value.storeId && filters.value.storeId.length > 0) {
    filtered = filtered.filter(store => filters.value.storeId.includes(store.loc_cd))
  }
  if (filters.value.group && filters.value.group.length > 0) {
    filtered = filtered.filter(store => filters.value.group.includes(store.grp_nm))
  }
  const uniqueDepartments = [...new Set(filtered.map(store => store.dpt_nm))]
  return uniqueDepartments.map(d => ({ value: d, label: d }))
})

const classOptions = computed(() => {
  let filtered = filtersData.value
  // If storeId is selected, filter by storeId
  if (filters.value.storeId && filters.value.storeId.length > 0) {
    filtered = filtered.filter(store => filters.value.storeId.includes(store.loc_cd))
  }
  // If group is selected, filter by group
  if (filters.value.group && filters.value.group.length > 0) {
    filtered = filtered.filter(store => filters.value.group.includes(store.grp_nm))
  }
  // If department is selected, filter by department
  if (filters.value.department && filters.value.department.length > 0) {
    filtered = filtered.filter(store => filters.value.department.includes(store.dpt_nm))
  }
  // Get unique classes
  const uniqueClasses = [...new Set(filtered.map(store => store.clss_nm))]
  return uniqueClasses.map(c => ({ value: c, label: c }))
})

const subclassOptions = computed(() => {
  let filtered = filtersData.value;
  // If storeId is selected, filter by storeId
  if (filters.value.storeId && filters.value.storeId.length > 0) {
    filtered = filtered.filter(store => filters.value.storeId.includes(store.loc_cd));
  }
  // If group is selected, filter by group
  if (filters.value.group && filters.value.group.length > 0) {
    filtered = filtered.filter(store => filters.value.group.includes(store.grp_nm));
  }
  // If department is selected, filter by department
  if (filters.value.department && filters.value.department.length > 0) {
    filtered = filtered.filter(store => filters.value.department.includes(store.dpt_nm));
  }
  // If class is selected, filter by class
  if (filters.value.class && filters.value.class.length > 0) {
    filtered = filtered.filter(store => filters.value.class.includes(store.clss_nm));
  }
  // Get unique subclasses
  const uniqueSubclasses = [...new Set(filtered.map(store => store.sub_clss_nm))];
  return uniqueSubclasses.map(s => ({ value: s, label: s }));
});


const outlierStatusOptions = [
  { value: "MAJOR_OUTLIER", label: "Major Outlier" },
  { value: "MINOR_OUTLIER", label: "Minor Outlier" },
  { value: "NON_OUTLIER", label: "Non Outlier" },
];

// Data
const originalRows = ref([]); // Keep original data separate
const loading = ref(false);
const isApprove = ref(false);
const isReject = ref(false);
const error = ref(null);
// Pagination
const currentPage = ref(1);
const pageSize = 20;
const totalCount = ref(0);
const isDownload = ref(false);

// Chart Modal
const showChartModal = ref(false);
const selectedRow = ref(null);
const performanceMetric = ref(sessionStorage.getItem("performance_metric"));
// Computed properties for pagination
const totalPages = computed(() => Math.ceil(totalCount.value / pageSize));
const paginatedRows = computed(() => originalRows.value);

const formatMonth = (yyyymm) => {
  const year = yyyymm.slice(0, 4);
  const month = yyyymm.slice(4);
  const date = new Date(`${year}-${month}-01`);
  return date.toLocaleString('default', { month: 'short', year: 'numeric' }); // e.g., "Aug 2024"
};

const parseFormattedMonth = (formatted) => {
  const date = new Date(formatted);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  return `${year}${month}`;
};


async function exportData() {
  isDownload.value = true
  const storeList = filters.value.storeId?.length > 0
    ? filters.value.storeId
    : getFallbackStore();

  if (!storeList) {
    return;
  }

  try {
    const params = {
      concept: sessionStorage.getItem("concept"),
      scenario_id: sessionStorage.getItem("scenario_id"),
      territory: sessionStorage.getItem("territory_name"),
      performance_metric: performanceMetric.value,
      loc_cd: storeList,
      cluster: filters.value.cluster,
      group: filters.value.group,
      department: filters.value.department, 
      class_field: filters.value.class,
      sub_class: filters.value.subclass,
      outlier_status: filters.value.outlierStatus,
    };

    // Await axios call and get JSON data from response.data
    const response = await axios.get("scenario/downloadOutlierData/", { params });

    // Extract data from response
    const data = response.data.data;

    if (!data || data.length === 0) {
      return;
    }

    // Use the fetched data to build CSV
    const performanceLabel = `${performanceMetric.value === 'REVENUE' ? 'Revenue' : 'GMV'}/day`;
    const columns = [
    { label: "Cluster", key: "cluster_num" },
    { label: "Store", key: "loc_nm" },
    { label: "Group", key: "grp_nm" },
    { label: "Department", key: "dpt_nm" },
    { label: "Class", key: "clss_nm" },
    { label: "Subclass", key: "sub_clss_nm" },
    { label: "Month", key: "month" },
    { label: "LM", key: "total_lm" },
    { label: "LM Contribution", key: "lm_contribution_in_store" },
    { label: performanceLabel, key: "per_day" },
    { label: "Outlier Status", key: "outlier_status" },
    { label: "Revised LM", key: "suggested_total_lm" },
    { label: "Final Status", key: "outlier_status_final" },
  ];


    // CSV header
    const header = columns.map(col => col.label).join(",");

    // CSV rows
    const dataRows = data.map(row => {
      return columns.map(col => {
        let val = row[col.key];

        if (col.key === "month" && typeof val === "string" && /^\d{6}$/.test(val)) {
          const year = val.slice(0, 4);
          const monthNum = parseInt(val.slice(4, 6), 10);
          const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun",
            "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
          const monthName = monthNames[monthNum - 1];
          val = `="${monthName} ${year}"`;
        }

        if (typeof val === "number") val = val.toFixed(2);
        if (typeof val === "string" && (val.includes(",") || val.includes('"'))) {
          val = `"${val.replace(/"/g, '""')}"`;
        }
        return val ?? "";
      }).join(",");
    });

    // Full CSV content
    const csvContent = [header, ...dataRows].join("\r\n");

    // Download CSV
    const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.href = url;
    link.download = `filtered_outlier_data_${new Date().toISOString().slice(0,10)}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

  } catch (err) {
    console.error("Error downloading CSV:", err);
  }
  finally{
    isDownload.value = false
  }
}


let filtersData = ref([]);
let storeDataAvailable = ref([]);
let dataDumpForFiltering = ref([]);
const fetchOutlierData = async (isAppend = false) => {
  error.value = null;
  try {
    if (!isAppend) loading.value = true;

    const response = await axios.get("scenario/outlier/", {
      params: {
        concept: sessionStorage.getItem('concept'),
        scenario_id: sessionStorage.getItem('scenario_id'),
        territory: sessionStorage.getItem('territory_name'),
        performance_metric: performanceMetric.value,
        loc_cd: (filters.value.storeId && filters.value.storeId.length > 0)
          ? filters.value.storeId
          : getFallbackStore(),
        cluster: filters.value.cluster,
        group: filters.value.group,
        department: filters.value.department, 
        class_field: filters.value.class,
        sub_class: filters.value.subclass,
        outlier_status: filters.value.outlierStatus,
        page: currentPage.value,
        limit: pageSize
      },
    });
    filtersData.value = response.data.filters || [];
    storeDataAvailable.value = response.data.selected_loc_cd   
    // Auto-populate storeId with fallback on first load
    if ((!filters.value.storeId || filters.value.storeId.length === 0) && !isAppend) {
      const fallbackStore = getFallbackStore();
      if (fallbackStore) {
        filters.value.storeId = [fallbackStore];
      }
    }
    const mappedData = response.data.data.map((item) => ({
      group: item.grp_nm,
      department: item.dpt_nm,
      class: item.clss_nm,
      subclass: item.sub_clss_nm,
      storeId: item.loc_cd,
      storeName: item.loc_nm,
      month: item.month,
      totalLm: item.total_lm,
      lmContrib: `${item.lm_contribution_in_store}%`,
      perDay: item.per_day,
      outlierStatus: item.outlier_status || "NON_OUTLIER",
      outlierFinalStatus: item.outlier_status_final,
      suggestedTotalLm: item.suggested_total_lm,
      action: "APPROVE", // Default action
      processing: false, // For individual row loading states
      cluster: item.cluster_num,
      new_cluster: item.new_cluster_num,
    }));
    // Store original data
    dataDumpForFiltering.value = mappedData;
    // originalRows.value = mappedData;
     if (isAppend) {
      originalRows.value = [...originalRows.value, ...mappedData];
    } else {
      originalRows.value = mappedData;
    }
    totalCount.value = response.data.count || 0;
  } catch (err) {
    console.error("Error fetching outlier data:", err);
    error.value =
      err.response?.data?.message ||
      err.message ||
      "Failed to load outlier data";
  } finally {
    loading.value = false;
  }
};

const isOutlier = (status) => {
  return status === "MAJOR_OUTLIER";
  return ["MAJOR_OUTLIER"].includes(status);
}

const getOutlierStatusClass = (status) => {
  switch (status) {
    case "NON_OUTLIER":
      return "bg-green-200 text-green-700 px-0.5 py-0.5 rounded text-xs font-semibold whitespace-nowrap";
    case "MINOR_OUTLIER":
      return "bg-neutral text-tx-primary px-0.5 py-0.5 rounded text-xs whitespace-nowrap";
    case "MAJOR_OUTLIER":
      return "bg-negative text-tx-primary px-0.5 py-0.5 rounded text-xs whitespace-nowrap";
    default:
      return "bg-gray-500 text-tx-primary px-0.5 py-0.5 rounded text-xs";
  }
};

// Filter functions
const applyFilters = async () => {
  currentPage.value = 1;
  outlierLoadingMessage.value = 'Applying filters...'

  await fetchOutlierData();
  await nextTick();
  checkNeedForPagination();
};

const handelClusterChange = () => {
  filters.value.storeId = [];
  filters.value.group = [];
  filters.value.department = [];
  filters.value.class = [];
  filters.value.subclass = [];
  filters.value.outlierStatus = '';
  // Set storeId to fallback store of new cluster
  const fallbackStore = getFallbackStore();
  filters.value.storeId = fallbackStore ? [fallbackStore] : [];
}
const handelStoreChange = () => {
  filters.value.group = [];
  filters.value.department = [];
  filters.value.class = [];
  filters.value.subclass = [];
  filters.value.outlierStatus = '';
}

const handelGroupChange = () => {
  filters.value.department = [];
  filters.value.class = [];
  filters.value.subclass = [];
  filters.value.outlierStatus = '';
};

const handelDepartmentChange = () => {
  filters.value.class = [];
  filters.value.subclass = [];
  filters.value.outlierStatus = '';
};
const resetFilters = () => {
  filters.value.group = [];
  filters.value.department = [];
  filters.value.class = [];
  filters.value.subclass = [];
  // filters.value.storeId = [];
  filters.value.outlierStatus = '';
  // filters.value.cluster = 0;
};

const getLabelByValue = (value) => {
  const option = outlierStatusOptions.find((opt) => opt.value === value);
  return option ? option.label : null;
};

// Pagination functions
const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
    fetchOutlierData();
  }
};

const previousPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    fetchOutlierData();
  }
};

// API functions for approve/reject
const updateOutlierStatus = async (rowsToUpdate, finalStatus) => {
  try {
    const rowsArray = Array.isArray(rowsToUpdate)
      ? rowsToUpdate
      : [rowsToUpdate];

    const perfMetric = sessionStorage.getItem("performance_metric");

    const payload = rowsArray.map((row) => {
      const base = {
        sub_clss_nm: row.subclass,
        loc_cd: row.storeId,
        outlier_status_final: finalStatus,
        // month: parseFormattedMonth(row.month),
        month: row.month,
        concept: sessionStorage.getItem("concept"),
        scenario_id: sessionStorage.getItem("scenario_id"),
      };

      if (perfMetric === "GMV") {
        base.gmv_suggested_total_lm = row.suggestedTotalLm;
      } else if (perfMetric === "REVENUE") {
        base.rev_suggested_total_lm = row.suggestedTotalLm;
      }

      return base;
    });
    const response = await axios.patch("scenario/outlier/", payload);

    if (response.status === 200) {
      // Update the row status locally
      rowsArray.forEach((row) => {
        row.outlierFinalStatus = finalStatus;
      });
      return true;
    }
  } catch (err) {
    console.error("Error updating outlier status:", err);
    error.value =
      err.response?.data?.message || err.message || "Failed to update status";
    return false;
  }
};

const updateAllOutlierStatus = async (finalStatus) => {
  try {
    
    const rowsArray = Array.isArray(originalRows)
      ? originalRows
      : [originalRows];

    const perfMetric = sessionStorage.getItem("performance_metric");

    const payload = {
      scenario_id: sessionStorage.getItem("scenario_id"),
      concept: sessionStorage.getItem("concept"),
      final_status: finalStatus,
      cluster: filters.value.cluster,
      loc_cd: filters.value.storeId,
      group: filters.value.group,
      department: filters.value.department,
      class: filters.value.class,
      subclass: filters.value.subclass,
      bulk_approve: true,
    };


    const response = await axios.patch("scenario/outlier/", payload);

    if (response.status === 200) {
      // Update the row status locally
      rowsArray.forEach((row) => {
        row.outlierFinalStatus = finalStatus;
      });
      return true;
    }
  } catch (err) {
    console.error("Error updating outlier status:", err);
    error.value =
      err.response?.data?.message || err.message || "Failed to update status";
    return false;
  }
};


const getAllOutliers = () => {
  return originalRows.value.filter((row) => isOutlier(row.outlierStatus));
};

const approveRow = async (row) => {
  row.processing = true;
  row.processingApprove = true;
  row.processingReject = false;
  const success = await updateOutlierStatus([row], "APPROVED");
  if (success) {
    row.outlierFinalStatus = "APPROVED";
    // Also update the original data to maintain consistency
    const originalRow = originalRows.value.find(
      (r) =>
        r.subclass === row.subclass &&
        r.storeId === row.storeId &&
        r.month === row.month
    );
    if (originalRow) {
      originalRow.outlierFinalStatus = "APPROVED";
    }
  }
  await setOptimizerRunStatus({
    scenario_id: sessionStorage.getItem('scenario_id'),
    run_optimizer: 0,
    run_performance: 0,
    run_range: 0
  })
  row.processing = false;
  row.processingApprove = false;
};

const rejectRow = async (row) => {
  row.processing = true;
  row.processingReject = true;
  row.processingApprove = false;
  const success = await updateOutlierStatus([row], "REJECTED");
  if (success) {
    row.outlierFinalStatus = "REJECTED";
    // Also update the original data to maintain consistency
    const originalRow = originalRows.value.find(
      (r) =>
        r.subclass === row.subclass &&
        r.storeId === row.storeId &&
        r.month === row.month
    );
    if (originalRow) {
      originalRow.outlierFinalStatus = "REJECTED";
    }
  }
  await setOptimizerRunStatus({
    scenario_id: sessionStorage.getItem('scenario_id'),
    run_optimizer: 0,
    run_performance: 0,
    run_range: 0
  })
  row.processing = false;
  row.processingReject = false;
};

// Bulk approve all outliers in current filtered view
const approveAll = async () => {
  isApprove.value = true
  const outlierRows = originalRows.value;
  if (originalRows.value.length === 0) {
  console.log("No major outliers found to approve.");
  isApprove.value = false;
  return;
}
  // Set processing state
  outlierRows.forEach((row) => (row.processing = true));
  const success = await updateAllOutlierStatus("APPROVED");
  if (success) {
    outlierRows.forEach((row) => {
      row.outlierFinalStatus = "APPROVED";
      // Also update the original data to maintain consistency
      const originalRow = originalRows.value.find(
        (r) =>
          r.subclass === row.subclass &&
          r.storeId === row.storeId &&
          r.month === row.month
      );
      if (originalRow) {
        originalRow.outlierFinalStatus = "APPROVED";
      }
    });
  }
  await setOptimizerRunStatus({
    scenario_id: sessionStorage.getItem('scenario_id'),
    run_optimizer: 0,
    run_performance: 0,
    run_range: 0
  })
  outlierRows.forEach((row) => (row.processing = false));
  isApprove.value = false
};

// Bulk reject all outliers in current filtered view
const rejectAll = async () => {
  isReject.value = true
  const outlierRows = originalRows.value
  if (originalRows.value.length === 0) {
  console.log("No major outliers found to reject.");
  isReject.value = false;
  return;
}
  // Set processing state
  outlierRows.forEach((row) => (row.processing = true));
  const success = await updateAllOutlierStatus("REJECTED");
  if (success) {
    outlierRows.forEach((row) => {
      row.outlierFinalStatus = "REJECTED";
      // Also update the original data to maintain consistency
      const originalRow = originalRows.value.find(
        (r) =>
          r.subclass === row.subclass &&
          r.storeId === row.storeId &&
          r.month === row.month
      );
      if (originalRow) {
        originalRow.outlierFinalStatus = "REJECTED";
      }
    });
  }
  await setOptimizerRunStatus({
    scenario_id: sessionStorage.getItem('scenario_id'),
    run_optimizer: 0,
    run_performance: 0,
    run_range: 0
  })
  outlierRows.forEach((row) => (row.processing = false));
  isReject.value = false
};

// Chart Modal Functions
const openChart = () => {
  showChartModal.value = true;
};

const closeChart = () => {
  showChartModal.value = false;
};

onMounted(async () => {
  await fetchOutlierData();
  checkNeedForPagination();
});
</script>

<style scoped>
/* Removed box-shadow to prevent double border effect */
</style>