<script setup>
import { ref, computed, onMounted, customRef, watch, defineExpose } from 'vue'
import Multiselect from 'vue-multiselect'
import axios from 'axios'
import DynamicFilter from './common/DynamicFilter.vue'
import { useStepsStore } from '../stores/NavigationStore.js'
import { getPreOptAPI, updateScenarioStatus,setOptimizerRunStatus  } from "../services/api"
import  PopupModal from './PopUpModal.vue'

const stepsStore = useStepsStore()
let testControlMappings = ref([])
const props = defineProps({
  scenarioPhase: {
    type: String,
    default: ''
  }
})
const isDisabled = computed(() => props.scenarioPhase === 'completed')

const formatKey = (key) => {
  if (!key) return '';
  if (key === "SEAC" || key === "ISC") return key;
  return key
    .toLowerCase()
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Call API when component is mounted
onMounted(async () => {
  loading.value = true;
  await fetchTestAndControlStores()
  loading.value = false;
})

let testAndControlStore = ref(null)
const storeOnSimilarity = ref([])
const scenarioDetails = stepsStore.getScenarioData
let loading = ref(false)

const storeMap = () => {
  const map = {}
  testAndControlStore.value.forEach(store => {
    map[store.loc_cd] = {
      loc_nm: store.loc_nm,
      cluster_num: store.cluster_num,
      new_cluster_num: store.new_cluster_num
    }
  })
  return map
}

// Flattened data for table
const storePairs = () => {
  storeOnSimilarity.value = []

  testAndControlStore.value.forEach(testStore => {
    if (!testStore.similarity_scores) return

    const scores = Object.entries(testStore.similarity_scores)
    // Sort scores descending by similarity
    scores.sort((a, b) => b[1] - a[1])
    for (const [controlCd, score] of scores) {
      const control = storeMap()[controlCd]
      if (!control) continue
      storeOnSimilarity.value.push({
        testStore: `${testStore.loc_cd}`,
        testNm : `${testStore.loc_nm}`,
        controlStore: `${controlCd}`,
        controlNM: `${control.loc_nm}`,
        similarityScore: Math.round(score * 100),
        testCluster: testStore.new_cluster_num,
        controlCluster: control.new_cluster_num
      })
    }
  })
  // Sort the final array by similarityScore descending
  storeOnSimilarity.value.sort((a, b) => b.similarityScore - a.similarityScore)
}
const parseJSONSafely = (jsonString) => {
  try {
    return typeof jsonString === 'string' ? JSON.parse(jsonString) : jsonString;
  } catch (e) {
    console.error('Error parsing JSON:', e);
    return {};
  }
};
const fetchTestAndControlStores = async () => {
  loading.value = true;
  try {
    const response = await axios.post('/scenario/testAndControlStore/', {
      concept: localStorage.getItem('concept') || 'hb',
      territory: sessionStorage.getItem('territory_name') || 'ae',
      scenario_id:scenarioDetails.id || scenarioDetails.scenario_id
    })

    // testAndControlStore.value = response.data
    // storePairs()
    testAndControlStore.value = response.data.map(store => ({
      ...store,
      similarity_scores: parseJSONSafely(store.similarity_scores),
      volume_contribution: parseJSONSafely(store.volume_contribution),
      ethnicity_contribution: parseJSONSafely(store.ethnicity_contribution)
    }));
    
    storePairs()
    if(testControlMappings.value.length > 0){
      console.log('calling from api')
      generateStoreComparisonData()
    }
    loading.value = false;
  } catch (err) {
    console.error('Error fetching test and control stores:', err)
    loading.value = false;
  }
}
const showPopup = ref(false)
const popupMessage = ref('')
const insertTestControlStores = async () => {
  loading.value = true
  if (testControlMappings.value.length === 0) {
    showPopup.value = true
    popupMessage.value = "Please select at least one test and control store mapping before proceeding."
    loading.value = false
    return false
  }
  try {
    sessionStorage.setItem('testControlMappings', JSON.stringify(testControlMappings.value))
    sessionStorage.setItem('loc_codes', JSON.stringify(testControlMappings.value.flatMap(mapping => [mapping.testStore, ...(mapping.controlStores || [])])))
    const formData = stepsStore.getScenarioData
    formData.storeSelection = testControlMappings.value
    const response = await axios.post('/scenario/insertTestControlStr/', {
      store_codes: JSON.stringify(testControlMappings.value),
      scenario_id: sessionStorage.getItem('scenario_id')
    })
    if(sessionStorage.getItem('cluster_changed') === 'true') {
      await checkAndRunOutlier()
    }
    testAndControlStore.value = response.data
    return true
  } catch (err) {
    console.error('Error fetching data:', err)
    return false
  }
  finally {
    loading.value = false
  }
}
// Expose async saveAndProceed for parent to await
async function saveAndProceed() {
  return await insertTestControlStores();
}
const checkAndRunOutlier = async () => {
  try {
    const response = await axios.post('/scenario/runOutliers/', {
      concept: localStorage.getItem('concept'),
      territory: sessionStorage.getItem('territory_name'),
      scenario_id: sessionStorage.getItem('scenario_id'),
      performance_metric: sessionStorage.getItem("performance_metric")
    })
    sessionStorage.setItem('cluster_changed', false);
    console.log(response.data)
  } catch (err) {
    console.error('Error running outliers:', err)
  }
}

const selectedTestStores = ref([])
const selectedControlStores = ref([])
const uniqueTestStores = () => {
  const data = testAndControlStore?.value
  if (!Array.isArray(data) || data.length === 0) return []

  // Step 1: Extract test and control store codes into a Set
  const excludedStores = new Set()
  if (testControlMappings.value.length > 0) {
    testControlMappings?.value?.forEach(mapping => {
      if (mapping.testStore) excludedStores.add(mapping.testStore)
      if (Array.isArray(mapping.controlStores)) {
        mapping.controlStores.forEach(store => excludedStores.add(store))
      }
    })
  }
  // Step 2: Filter duplicates and excluded stores
  const seen = new Set()
  const filterData = data.filter(store => {
    const locCode = store.loc_cd
    if (!seen.has(locCode) && !excludedStores.has(locCode)) {
      seen.add(locCode)
      return true
    }
    return false
  }).map(store => ({
    value: `${store.loc_cd}`,
    label: `${store.loc_cd} - ${store.loc_nm}`
  }))

  return filterData
}

testControlMappings = computed(() => {
  const storedData = sessionStorage.getItem('testControlMappings')
  if (!storedData) return []

  try {
    const parsed = JSON.parse(storedData)   // first parse
    console.log(parsed, 'doubleParsed')
    return Array.isArray(parsed) ? parsed : []
  } catch {
    return []
  }
})

// Watch for changes in testControlMappings and call generateStoreComparisonData after compute
watch(testControlMappings, () => {
  if(testAndControlStore?.value?.length > 0) {
    console.log(testControlMappings.value, 'testControlMappings changed')
    generateStoreComparisonData()
  }
}, { immediate: true, deep: true })

const getAvailableControlStores = () => {
  const data = testAndControlStore?.value
  if (!Array.isArray(data) || data.length === 0) return []

  // Collect all test and control stores into a Set to exclude
  const excludedStores = new Set()
  for (const mapping of testControlMappings.value || []) {
    if (mapping.testStore) excludedStores.add(mapping.testStore)
    if (Array.isArray(mapping.controlStores)) {
      for (const store of mapping.controlStores) {
        excludedStores.add(store)
      }
    }
  }

  return data
    .filter(store =>
      !excludedStores.has(store.loc_cd) && !selectedTestStores?.value?.includes(store.loc_cd)
    )
    .map(store => ({
      value: store.loc_cd,
      label: `${store.loc_cd} - ${store.loc_nm}`
    }))
}



function customStoreLabel(option) {
  return `${option.loc_cd} - ${option.loc_nm}`
}

function getAllSelectedLocCds() {
  if (!selectedTestStores.value || selectedControlStores.value.length === 0) {
    showPopup.value = true
    popupMessage.value = "Please select both test and control store mapping before proceeding."
    return
  }
  if (testControlMappings.value.length === 2) {
    showPopup.value = true
    popupMessage.value = "You can select max of 2 test and control store mapping."
    return
  }
  testControlMappings.value.push({
    testStore: selectedTestStores.value,
    controlStores: [...selectedControlStores.value]
  })
  // Generate store comparison data
  generateStoreComparisonData()
  // Reset selections for next mapping
  selectedTestStores.value = null
  selectedControlStores.value = []
  setOptimizerRunStatus({
    scenario_id: sessionStorage.getItem("scenario_id"),
    run_optimizer: 0,
    run_performance: 0,
    run_range: 0,
  });
  if (typeof stepsStore !== 'undefined' && stepsStore.steps) {
    stepsStore.steps.forEach((step, index) => {
      if (step.name === 'Review Outliers' ||
        step.name === 'Performance Score' ||
        step.name === 'Optimize'
      ) {
        step.disabled = true;
        step.hidden = false;
      } else {
        step.disabled = false;
        step.hidden = false;
      }
    });
  }
}

const handelTestStoreChange = () => {
  selectedControlStores.value = []
}


// Reactive data for store comparison
const storeComparisonData = ref([])

// Computed properties for dynamic headers
const ethnicityKeys = computed(() => {
  const allKeys = new Set()
  storeComparisonData.value.forEach(store => {
    if (store.ethnicity_contribution) {
      Object.keys(store.ethnicity_contribution).forEach(key => allKeys.add(key))
    }
  })
  return Array.from(allKeys)
})

const volumeKeys = computed(() => {
  const allKeys = new Set()
  storeComparisonData.value.forEach(store => {
    if (store.volume_contribution) {
      Object.keys(store.volume_contribution).forEach(key => allKeys.add(key))
    }
  })
  return Array.from(allKeys)
})

const saveOptimiserProgress = async (action) => {
  try {
    await updateScenarioStatus({
      scenario_id: sessionStorage.getItem('scenario_id'),
      current_page: stepsStore.currentStep,
      progress_page: stepsStore.currentStep
    })
    if (action === 'next') {
    stepsStore.goToNextStep()
    } else if (action === 'prev') {
      stepsStore.goToPreviousStep()
    }
  } catch (err) {
    console.error('Error updating scenario status:', err)
  }
}

function generateStoreComparisonData() {
  const comparisonData = []

  testControlMappings.value.forEach(mapping => {
    // Get all store codes from mapping
    const allStoreCodes = [mapping.testStore, ...mapping.controlStores]
    const uniqueStoreCodes = [...new Set(allStoreCodes)] // Remove duplicates

    uniqueStoreCodes.forEach(storeCode => {
      // Find store data from scenarioDetails
      const storeData = testAndControlStore.value.find(
        store => store.loc_cd === storeCode
      )

      if (storeData) {
        // Determine store type
        let storeType = 'Control Store'
        // if (mapping.testStore.includes(storeCode)) {
        if (mapping.testStore === storeCode) {
          storeType = 'Test Store'
        }

        // Add store type to the data
        comparisonData.push({
          ...storeData,
          storeType: storeType
        })
      }
    })
  })

  storeComparisonData.value = comparisonData
}

// Helper methods
function formatNumber(value) {
  if (value === null || value === undefined || isNaN(value)) return '-';
  
  return new Intl.NumberFormat('en-AE', {
    maximumFractionDigits: 0
  }).format(Number(value));
}

function formatVolumeKey(key) {
  // Convert snake_case to Title Case
  return key.split('_').map(word =>
    word.charAt(0).toUpperCase() + word.slice(1).toLowerCase()
  ).join(' ')
}

// const deleteMapping = async (mappingIdx) => {
//   testControlMappings.value.splice(mappingIdx, 1)
//   generateStoreComparisonData()
//   const response = await axios.post('/scenario/insertTestControlStr/', {
//     store_codes: JSON.stringify(testControlMappings.value) || '',
//     scenario_id: sessionStorage.getItem('scenario_id')
//   })
// }

async function deleteMapping(mappingIdx) {
  try {
    loading.value = true;

    const payload = {
      scenario_id: sessionStorage.getItem('scenario_id'),
      concept: localStorage.getItem('concept'),
      territory: sessionStorage.getItem('territory_name'),
      store_codes: JSON.stringify(testControlMappings.value) || '[]'
    };

    const response = await axios.patch('/scenario/insertTestControlStr/', payload);

    testControlMappings.value.splice(mappingIdx, 1)
    generateStoreComparisonData()
    
    showPopup.value = true;
    popupMessage.value = "Mapping deleted successfully.";

  } catch (error) {
    console.error("Error deleting mapping:", error);
    showPopup.value = true;
    popupMessage.value = "Failed to delete mapping.";
  } finally {
    loading.value = false;
  }
}


defineExpose({ saveAndProceed });

</script>

<template>
  <div v-if="loading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div class="bg-white rounded-lg p-2 flex items-center justify-center">
        <div class="tw-loader"></div> 
        <div class="mr-3">Loading...</div>
      </div>
  </div>
  <div v-else>
    <div class="px-10 py-2 space-y-2">
      <!-- Top Section: Test & Control Store List -->
      <h3 class="text-xs font-semibold text-tertiary mb-2">Test & Control Store List</h3>
      <div class="bg-white rounded shadow h-[40vh] overflow-y-auto custom-scroll">
        <table class="w-full table-auto border-separate border-spacing-0 border-[0.5px] border-gray-300 text-xs text-left">
          <thead class="bg-primary text-xs tracking-wide sticky top-0 z-10">
            <tr>
              <th class="px-4 py-2 min-w-[80px] border border-gray-300 text-center font-semibold">Test Store</th>
              <th class="px-4 py-2 min-w-[80px] border border-gray-300 text-center font-semibold">Control Store</th>
              <th class="px-4 py-2 min-w-[80px] border border-gray-300 text-center font-semibold">Similarity Score</th>
              <th class="px-4 py-2 min-w-[80px] border border-gray-300 text-center font-semibold">Test Cluster</th>
              <th class="px-4 py-2 min-w-[80px] border border-gray-300 text-center font-semibold">Control Cluster</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(pair, index) in storeOnSimilarity" :key="index" class="border-t">
              <td class="px-4 py-2 border border-gray-300 text-center">{{ pair.testStore }} - {{pair.testNm }}</td>
              <td class="px-4 py-2 border border-gray-300 text-center">{{ pair.controlStore }} - {{pair.controlNM}}</td>
              <td class="px-4 py-2 border border-gray-300 text-center">{{ pair.similarityScore}}%</td>
              <td class="px-4 py-2 border border-gray-300 text-center">{{ pair.testCluster }}</td>
              <td class="px-4 py-2 border border-gray-300 text-center">{{ pair.controlCluster }}</td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Store Selection Dropdowns -->
      <div class="text-tertiary font-semibold">
        <h3>Select Test and Control Stores :</h3>
      </div>
      <div class="ml-2 flex items-center gap-4">
        <div class="space-y-1 w-1/3">
          <label class="block text-xs font-medium text-gray-600">Test Store</label>
          <DynamicFilter v-model="selectedTestStores" :multiselect="false" label="Groups" placeholder="Test Stores"
            :options="uniqueTestStores()" variant="secondary" size="sm" @change="handelTestStoreChange()" />
        </div>
        <div class="space-y-1 w-1/3">
          <label class="block text-xs font-medium text-gray-600">Control Store</label>
          <DynamicFilter v-model="selectedControlStores" :multiselect="true" label="Groups" :max-selections=2
            placeholder="Test Stores" :options="getAvailableControlStores()" variant="secondary" size="sm" />
        </div>
              <div class="w-1/3 mt-4">
                <button @click="getAllSelectedLocCds()"
                  :disabled="isDisabled"
                  :title="isDisabled ? 'Scenario completed — actions disabled' : ''"
                  class="bg-tertiary hover:bg-tertiary text-tx-primary font-semibold py-2 px-4 rounded-lg shadow-sm transition duration-200 disabled:opacity-60 disabled:cursor-not-allowed">
                  Apply
                </button>
              </div>
      </div>

      <!-- Bottom Section: Store Comparison Metrics -->
      <div>
        <h3 class="font-semibold text-tertiary mb-4">Store Comparison Metrics</h3>

        <div v-if="storeComparisonData.length > 0" class="bg-white rounded shadow overflow-x-auto custom-scroll">
          <table class="table-auto min-w-max border-separate border-spacing-0 border-[0.5px] border-gray-300">
            <!-- Main Headers -->
            <thead>
              <tr class="bg-gray-50 text-xs">
                <th
                  rowspan="2"
                  class="px-2 py-1 text-left border !border-gray-300 whitespace-nowrap font-semibold !z-20 !bg-white sticky left-0"
                  style="min-width: 60px;"
                >
                  Store
                </th>
                <th
                  rowspan="2"
                  class="px-2 py-1 text-left border !border-gray-300 whitespace-nowrap font-semibold !z-20 !bg-white sticky !left-[60px]"
                  style="min-width: 60px;"
                >
                  Store Type
                </th>
                <th rowspan="2" class="px-2 py-1 text-left border border-gray-300 whitespace-nowrap font-semibold">
                  Revenue</th>
                <th rowspan="2" class="px-2 py-1 text-left border border-gray-300 whitespace-nowrap font-semibold">Units
                </th>
                <th rowspan="2" class="px-2 py-1 text-left border border-gray-300 whitespace-nowrap font-semibold">
                  Customers</th>
                <th rowspan="2" class="px-2 py-1 text-left border border-gray-300 whitespace-nowrap font-semibold">
                  Invoices</th>
                <th rowspan="2" class="px-2 py-1 text-left border border-gray-300 whitespace-nowrap font-semibold">Area
                  SqFt</th>
                <th rowspan="2" class="px-2 py-1 text-left border border-gray-300 whitespace-nowrap font-semibold">
                  Rev/SqFt</th>
                <!-- Ethnicity Headers -->
                <th v-if="ethnicityKeys.length > 0" :colspan="ethnicityKeys.length"
                  class="px-2 py-1 text-center border border-gray-300 font-semibold bg-blue-100">
                  Customer Nationality Mix
                </th>

                <!-- Volume Headers -->
                <th v-if="volumeKeys.length > 0" :colspan="volumeKeys.length"
                  class="px-2 py-1 text-center border border-gray-300 font-semibold bg-orange-100">
                  Volume Contribution
                </th>

                <th class="px-2 py-1 text-center bg-gray-100 border border-gray-300 font-semibold"></th>
              </tr>

              <!-- Sub-Headers -->
              <tr class="bg-gray-50 text-xs">
                <!-- Ethnicity Sub Headers -->
                <th
                  v-for="key in ethnicityKeys"
                  :key="'eth-' + key"
                  class="px-2 py-1 border border-blue-100 text-center whitespace-nowrap font-semibold bg-blue-100"
                >
                  {{ formatKey(key) }}
                </th>

                <!-- Volume Sub Headers -->
                <th v-for="key in volumeKeys" :key="'vol-' + key"
                  class="px-2 py-1 border border-primary text-center whitespace-nowrap font-semibold bg-orange-100">
                  {{ formatKey(key) }}
                </th>

                <th class="border border-gray-300 font-semibold"></th>
              </tr>
            </thead>

            <!-- Table Body -->
            <tbody>
              <template v-for="(mapping, mappingIdx) in testControlMappings">
                <tr
                  v-for="store in storeComparisonData.filter(s => [mapping.testStore, ...mapping.controlStores].includes(s.loc_cd))"
                  :key="mapping.testStore + '-' + store.loc_cd"
                  class="text-xs hover:bg-primary odd:bg-white even:bg-gray-50 transition-colors"
                >
                  <!-- Left-Aligned Text Columns -->
                  <td
                    class="px-2 py-1 border border-gray-300 text-left sticky left-0 !z-20 !bg-white"
                    style="min-width: 60px;"
                  >
                    {{ store.loc_cd }}
                  </td>
                  <td
                    class="px-2 py-1 border border-gray-300 text-left sticky !left-[60px] !z-20 !bg-white"
                    style="min-width: 60px;"
                  >
                    {{ store.storeType }}
                  </td>

                  <!-- Right-Aligned Numeric Columns -->
                  <td class="px-2 py-1 border border-gray-300 text-center">{{ formatNumber(store.revenue) }}</td>
                  <td class="px-2 py-1 border border-gray-300 text-center">{{ formatNumber(store.units) }}</td>
                  <td class="px-2 py-1 border border-gray-300 text-center">{{ formatNumber(store.total_customer ??
                    store.customers) }}</td>
                  <td class="px-2 py-1 border border-gray-300 text-center">{{ formatNumber(store.total_invoice) }}</td>
                  <td class="px-2 py-1 border border-gray-300 text-center">{{ formatNumber(store.area_sqft) }}</td>
                  <td class="px-2 py-1 border border-gray-300 text-center">{{ formatNumber(store.revenue_per_sqft) }}
                  </td>

                  <!-- Ethnicity Data with % -->

                  <td v-for="key in ethnicityKeys" :key="'eth-data-' + key + '-' + store.loc_cd"
                    class="px-2 py-1 text-center border border-gray-300">
                    <!-- If key is National, show plain value, else append % -->
                    {{
                    store.ethnicity_contribution[key] !== undefined
                    ? key === 'NATIONALS'
                    ? Math.round(store.ethnicity_contribution[key]) + '%'
                    : Math.round(store.ethnicity_contribution[key]) + '%'
                    : '-'
                    }}
                  </td>


                  <!-- Volume Data with % -->
                  <td v-for="key in volumeKeys" :key="'vol-data-' + key + '-' + store.loc_cd"
                    class="px-2 py-1 text-center border border-gray-300">
                    {{ store.volume_contribution[key] !== undefined ? Math.round(store.volume_contribution[key]) + '%' :
                    '-' }}
                  </td>

                  <!-- Delete Button -->
                  <td class="px-2 py-1 text-center border border-gray-300">
                    <button v-if="testControlMappings.length > 0 && store.storeType === 'Test Store'" @click="deleteMapping(mappingIdx)"
                      :disabled="isDisabled"
                      :title="isDisabled ? 'Scenario completed — actions disabled' : ''"
                      class="bg-red-500 hover:bg-red-700 text-tx-primary px-3 py-1 rounded text-xs font-bold disabled:opacity-60 disabled:cursor-not-allowed">
                      Delete
                    </button>
                  </td>
                </tr>
              </template>
            </tbody>
          </table>
        </div>

        <!-- No Data Message -->
        <div v-else class="text-gray-500 text-center py-8">
          No store comparison data available. Please select and approve test and control stores.
        </div>
      </div>

    </div>
    <!-- <div class="flex justify-between m-4">
      <button @click="saveOptimiserProgress('prev')"
        class="border border-gray-300 text-tx-primary px-4 font-semibold py-2  mb-8 rounded hover:bg-gray-100 ">
        ← Previous
      </button>
      <button @click="insertTestControlStores()"
        class="bg-secondary hover:bg-tertiary text-tx-primary font-semibold mr-8 mb-8 cursor-pointer hover:bg-tertiary px-6 py-2 rounded mr-2 "
        :disabled="stepsStore.currentStep === stepsStore.steps.length - 1">
        Next →
      </button>
    </div> -->
  </div>
  <PopupModal :show="showPopup" :message="popupMessage" @close="showPopup = false" />

</template>

<style>
.custom-scroll::-webkit-scrollbar {
  width: 6px;
}

.custom-scroll::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.3);
  /* Customize color */
  border-radius: 9999px;
}

.custom-scroll::-webkit-scrollbar-track {
  background: transparent;
}

.custom-scroll {
  scrollbar-width: thin;
  /* Firefox */
  scrollbar-color: rgba(0, 0, 0, 0.3) transparent;
  /* Firefox */
}
</style>