<script setup lang="ts">
import { ref, inject } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();
const baseImgUrl = inject('baseImageUrl');
const concepts = [
  { name: 'Babyshop', key: 1, img: `${baseImgUrl}/babyshop.png` },
  { name: 'Homebox', key: 2, img: `${baseImgUrl}/homebox.png` },
  { name: 'Lifestyle', key: 3, img: `${baseImgUrl}/lifestyle.png` },
  { name: 'Splash', key: 4, img: `${baseImgUrl}/splash.png` }
];

const selectedConcept = ref<number | null>(null);
const hoveredConcept = ref<number | null>(null);

function selectConcept(key: number) {
  selectedConcept.value = key;
}

function continueAction() {
  if (!selectedConcept.value) return;
  localStorage.setItem('concept', selectedConcept.value.toString());
  router.push({ name: 'store-clustering' });
}
</script>

<template>
  <div class="flex h-screen w-full bg-[#004c99] items-center justify-center">
    <div class="relative flex flex-col items-center justify-center bg-[#004c99] rounded-md shadowCSS shadow-lg w-[90%] max-w-[500px] h-[380px]">
      <div class="absolute left-0 top-0 h-full w-4 rounded-l-md bg-[#99CCFF]"></div>
      <!-- Scrollable Concepts Selection -->
      <div class="w-full flex-1 flex flex-col items-center justify-center">
        <div class="w-3/4 max-h-[200px] overflow-y-auto px-2 custom-scrollbar">
          <div
            v-for="concept in concepts"
            :key="concept.key"
            class="flex items-center mb-3 cursor-pointer transition"
            :class="selectedConcept === concept.key ? 'bg-[#99CCFF] rounded-lg shadow p-2' : ''"
            @click="selectConcept(concept.key)"
            @mouseover="selectedConcept !== concept.key && (hoveredConcept = concept.key)"
            @mouseleave="hoveredConcept = null"
            :style="hoveredConcept === concept.key ? { backgroundColor: '#60A5FA', borderRadius: '6px', padding: '6px' } : {}"
          >
            <!-- Radio Button -->
            <input
              type="radio"
              :id="concept.key"
              :value="concept.key"
              v-model="selectedConcept"
              class="accent-[#F59E0B] h-4 w-4 mr-2"
              @click.stop="selectConcept(concept.key)"
            />
            <img
              :src="concept.img"
              :alt="concept.name"
              class="h-6 w-12 object-contain mr-2 rounded transition"
            />
            <span class="text-tx-primary text-m font-semibold">{{ concept.name }}</span>
          </div>
        </div>
        <button
          class="mt-4 px-6 py-2 rounded bg-[#F59E0B] text-tx-primary font-bold hover:bg-[#BFDBFE] disabled:opacity-50 disabled:cursor-not-allowed transition"
          :disabled="!selectedConcept"
          @click="continueAction"
        >
          Continue
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.shadowCSS {
  box-shadow: rgba(153, 204, 255, 0.6) 5px 5px 100px 0px, rgba(0, 0, 0, 0.06) 0px 0px 0px 1px;
}

.custom-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #3B82F6 #004c99;
}

.custom-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.custom-scrollbar::-webkit-scrollbar-track {
  background: #004c99;
}

custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #3B82F6;
  border-radius: 10px;
}
</style>