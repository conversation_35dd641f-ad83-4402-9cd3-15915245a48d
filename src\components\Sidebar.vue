<script setup lang="ts">
import { ref, inject, computed, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
const router = useRouter();

function handleLogout() {
  localStorage.clear();
  sessionStorage.clear();
  router.replace('/');
}
import {
  ChevronLeft,
  ChevronRight,
  FileText,
  BarChart2,
  ChartScatter,
  Settings,
  LogOut,
  Home,
  FileBox,
  Gauge,
  Boxes,
  Users,
  SquareMousePointer,
  Target,
  Outlier,
  Cog
} from 'lucide-vue-next';

const baseImgUrl = inject('baseImageUrl');

const cameFromBuildOptimiser = ref(false);



const props = defineProps({
  isExpanded: Boolean
});

const notificationCount = ref(3);
const username = computed(() => JSON.parse(localStorage.getItem('user') || ''));

const userInitial = computed(() => {
  if (!username.value) return '';
  // Take part before "@", split by "."
  const parts = username.value.split('@')[0].split('.');
  return parts.map(p => p.charAt(0).toUpperCase()).join('');
});

const isScenario = computed(() => 'isscenario' in route.query);



const userFullname = computed(() => {
  if (!username.value) return '';
  // Take part before "@", split by "."
  const parts = username.value.split('@')[0].split('.');
  return parts
    .map(p => p.charAt(0).toUpperCase() + p.slice(1).toLowerCase())
    .join(' ');
});
// const userInitial = computed(() => username.value.charAt(0).toUpperCase());

// Get user role from localStorage (or your auth system)
const userRole = computed(() => localStorage.getItem('role') || '');

const route = useRoute();

const buildOptimiserPaths = ['/BuildOptimiser', '/BuildOptimiser/DataSummary', '/BuildOptimiser/SpaceHealthDashboard'];

function getPathWithScenario(path: string, addStatus = false) {
  const isBuildPath = buildOptimiserPaths.some(p => path.startsWith(p));

  // if (isScenario.value && isBuildPath) {
    const query: Record<string, string> = { isscenario: 'true' };
    if (addStatus) query.status = 'create';
    return { path, query };
  // }

  // For all other routes, return plain path (no query params)
  return path;
}


const menuItems = computed(() => {
  const items = [
    {
      label: 'Optimization',
      path: getPathWithScenario('/HomePage'),
      icon: Cog
    }
  ];

  const isOnBuildOptimiserPath =
    route.path === '/BuildOptimiser' || route.path.startsWith('/BuildOptimiser/');

  // if (isScenario.value) {
    items.push({
      label: 'Scenario',
      path: getPathWithScenario('/BuildOptimiser', true), // includes status=create
      icon: Gauge
    });
  // }

  if (isOnBuildOptimiserPath || route.path === '/HomePage' || route.path === '/HomePage/') {
    items.push({
      label: 'Data Summary',
      path: getPathWithScenario('/BuildOptimiser/DataSummary'),
      icon: ChartScatter
    });
    items.push({
      label: 'Space Health',
      path: getPathWithScenario('/BuildOptimiser/SpaceHealthDashboard'),
      icon: BarChart2
    });
  }

  if (userRole.value === 'superadmin') {
    items.push({
      label: 'User Dashboard',
      path: getPathWithScenario('/user-dashboard'),
      icon: Users
    });
  }
  return items;
});
</script>

<template>
  <div class="h-full flex flex-col transition-all duration-300 bg-tertiary text-tx-primary shadow-lg">
    <!-- Top Section: Logo -->
    <div class="flex items-center p-4 border-b border-primary flex-shrink-0" :class="{ 'justify-center': !isExpanded }">
      <div class="w-10 h-10 flex items-center justify-center">
        <img  :src="`${baseImgUrl}/space_logo_01.png`" class="w-10 h-10" alt="Space Labs Logo" />
         <!-- <span> S </span> -->
      </div>
      <span v-if="isExpanded" class="ml-3 text-tx-primary text-lg font-semibold whitespace-nowrap">Space Labs</span>
    </div>

    <!-- Main Navigation Items - Scrollable if needed -->
    <nav class="flex-1 mt-6 space-y-2 px-3 overflow-y-auto">
      <router-link
        v-for="item in menuItems"
        :key="item.path"
        :to="item.path"
        class="flex items-center py-2 px-3 rounded-lg text-tx-primary hover:bg-primary hover:text-tx-primary transition-colors"
        :class="[
          { 'justify-center': !isExpanded },
          $route.path === item.path ? 'bg-primary text-secondary font-bold shadow' : ''
        ]"
      >
        <component :is="item.icon" class="w-5 h-5" />
        <span v-if="isExpanded" class="ml-3 text-sm font-medium whitespace-nowrap">
          {{ item.label }}
        </span>
      </router-link>
    </nav>

    <!-- Bottom Section: Settings, Logout, Profile - Fixed at bottom -->
    <div class="flex-shrink-0 px-3 pb-3 space-y-2">
      <!-- Settings -->
      <!-- <router-link
        to="/#"
        class="flex items-center py-2 px-3 rounded-lg text-tx-primary hover:bg-primary hover:text-secondary transition-colors"
        :class="{ 'justify-center': !isExpanded }"
      >
        <Settings class="w-5 h-5" />
        <span v-if="isExpanded" class="ml-3 text-sm font-medium whitespace-nowrap">
          Settings
        </span>
      </router-link> -->

      <!-- Log out -->
      <router-link
        to="/#"
        class="flex items-center py-2 px-3 rounded-lg text-tx-primary hover:bg-primary hover:text-tx-primary transition-colors"
        :class="{ 'justify-center': !isExpanded }"
        @click.prevent="handleLogout"
      >
        <LogOut class="w-5 h-5" />
        <span v-if="isExpanded" class="ml-3 text-sm font-medium whitespace-nowrap text-tx-primary">
          Log out
        </span>
      </router-link>

      <!-- User Profile -->
      <div
        class="flex items-center py-2 px-3 bg-secondary text-tx-primary rounded-lg"
        :class="{ 'justify-center': !isExpanded }"
      >
        <div class="w-8 h-8 flex items-center justify-center bg-primary text-tx-primary rounded-full text-sm font-bold">
          {{ userInitial }}
        </div>
        <span v-if="isExpanded" class="ml-3 text-sm text-tx-primary font-medium whitespace-nowrap">
          {{ userFullname }}
        </span>
      </div>
    </div>
  </div>
</template>