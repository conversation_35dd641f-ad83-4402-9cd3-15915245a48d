apiVersion: apps/v1
kind: Deployment
metadata:
  name: radar-fastapi-backend
  namespace: dll-radar
spec:
  replicas: 1
  selector:
    matchLabels:
      app: radar-fastapi-backend
  template:
    metadata:
      labels:
        app: radar-fastapi-backend
    spec:
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: agentpool
                operator: In
                values: 
                - usrnodepl01
      tolerations:
        - key: "a"
          operator: "Equal"
          value: "b"
          effect: "NoSchedule"
      containers:
        - name: radar-fastapi-backend
          image: lmapaz1acrdllprd02.azurecr.io/radar/radar-fastapi-backend-img
          ports:
            - containerPort: 8000
          env:
            - name: ENDPOINT
              valueFrom:
                configMapKeyRef:
                  name: radar-fastapi-app-config
                  key: ENDPOINT
            
            - name: DB_USER
              valueFrom:
                configMapKeyRef:
                  name: radar-fastapi-app-config
                  key: DB_USER
            
            - name: DB_NAME
              valueFrom:
                configMapKeyRef:
                  name: radar-fastapi-app-config
                  key: DB_NAME

            - name: DB_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: radar-dev-db
                  key: radar-dev-db-key
           
            - name: ROOT_PATH
              valueFrom:
                configMapKeyRef:
                  name: radar-fastapi-app-config
                  key: ROOT_PATH
    

          volumeMounts:
             - name: secrets-volume
               mountPath: "/mnt/secrets-store"
               readOnly: true
      volumes:
        - name: config-volume
          configMap:
            name: radar-fastapi-app-config
        - name: secrets-volume
          csi:
            driver: secrets-store.csi.k8s.io
            readOnly: true
            volumeAttributes:
              secretProviderClass: "azure-radar-fastapi-secret-provider"
           