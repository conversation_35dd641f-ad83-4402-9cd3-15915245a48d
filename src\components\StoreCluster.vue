<template>
  <div class="flex-1 px-4 py-4 sm:px-6 sm:py-3 lg:px-8 lg:py-2">
    <div class="flex justify-end mb-3 gap-3">

  <button
    @click="exportData()"
    class="flex items-center gap-2 py-1 px-2 bg-tertiary hover:bg-tertiary text-tx-primary font-semibold rounded-lg shadow transition"
  >
    <div v-if="isDownload" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
    <Download v-else class="w-4 h-4" />
  </button>
  <div class="relative group inline-block">
  <button
    @click="resetClusters"
    :disabled="disableReset"
    class="flex items-center gap-2 py-1 px-2 bg-tertiary hover:bg-tertiary text-tx-primary font-semibold rounded-lg shadow transition 
    disabled:bg-gray-300 disabled:text-gray-500 disabled:cursor-not-allowed"
  >
    <RotateCcw class="w-4 h-4"/> 
  </button>

  <!-- Tooltip above button -->
  <div
    class="absolute bottom-full left-1/2 -translate-x-1/2 mb-3
           px-4 py-2 bg-slate-800 text-white text-sm rounded-md shadow-lg
           opacity-0 pointer-events-none transition-all duration-300 ease-out
           group-hover:opacity-100 group-hover:pointer-events-auto group-hover:-translate-y-1
           after:content-[''] after:absolute after:top-full after:left-1/2
           after:-translate-x-1/2 after:border-4 after:border-transparent
           after:border-t-slate-800"
  >
    <div class="whitespace-nowrap font-medium">Reset to Default</div>
  </div>
</div>

</div>

    <div class="rounded shadow">
      <div class="w-full flex flex-col rounded overflow-x-auto max-w-full">
        <div v-if="loading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg p-2 flex items-center justify-center">
            <div class="tw-loader"></div> 
            <div class="mr-3">Loading...</div>
          </div>
    </div>
      <div v-else>
        <table class="w-full table-auto border-collapse border border-gray-300 z-10">
          <thead class="bg-primary text-[13px] tracking-wide sticky top-0 ">
            <tr>
              <th rowspan="2" class=" border border-gray-300 sticky left-0  bg-primary">Cluster</th>
              <th rowspan="2" colspan="2" class="py-1 px-2 border border-gray-300 text-center sticky left-[52px] bg-primary">Action</th>
              <th rowspan="2" class="py-1 px-2 border border-gray-300 sticky left-[120px] bg-primary">Store</th>
              <th rowspan="2" class="py-1 px-2 border border-gray-300 sticky left-[170px] bg-primary">Store Name</th>
              <th rowspan="2" class="py-1 px-2 border border-gray-300">
                {{ performanceMetric.charAt(0).toUpperCase() + performanceMetric.slice(1).toLowerCase() }}
              </th>
              <th rowspan="2" class="py-1 px-2 border border-gray-300">
                 Units
              </th>
              <th rowspan="2" class="py-1 px-2 border border-gray-300">Customers</th>
              <th rowspan="2" class="py-1 px-2 border border-gray-300">Invoices</th>
              <th rowspan="2" class="py-1 px-2 border border-gray-300">
                Area SqFt
              </th>
              <th rowspan="2" class="py-1 px-2 border border-gray-300">
                Rev/SqFt
              </th>
              
              
              <th v-if="ethnicityKeys.length > 0" :colspan="ethnicityKeys.length"
                  class="py-1 px-2 text-center border border-gray-300 bg-blue-100">
                  Customer Nationality Mix
                </th>
                <!-- Dynamic Volume Headers -->
                <th v-if="volumeKeys.length > 0" :colspan="volumeKeys.length"
                  class="py-1 px-2 text-center  border border-gray-300 bg-orange-100">
                  Volume Contribution
                </th>
              
              
            </tr>
            <tr class="bg-gray-50 text-[13px]">
                <th v-for="key in ethnicityKeys" :key="'eth-' + key"
                  class="px-2 py-1 text-[13px] border border-gray-300 bg-blue-100">
                  {{ formatKey(key) }}
                </th>
                <!-- Volume Sub Headers -->
                <th v-for="key in volumeKeys" :key="'vol-' + key"
                  class="px-2 py-1 text-[13px] border border-gray-300 bg-orange-100">
                  {{ formatKey(key) }}
                </th>
                <th class="border border-gray-300"></th>
              </tr>
          </thead>

          <tbody>
            <template v-for="(stores, cluster) in storesByCluster" :key="cluster">
              <!-- Collapsed totals row -->
              <tr v-if="!expandedClusters.has(cluster)"
                class="font-semibold bg-gray-100 text-[13px] cursor-pointer select-none hover:bg-gray-200"
                @click="toggleCluster(cluster)">
                <td class="py-1 px-2 border border-gray-300 text-center">
                  Total ({{ stores.length }})
                </td>
                <td class="px-2 border border-gray-300"></td>
                <td class="px-3 border border-gray-300"></td>

                <td class="py-1 px-2 border border-gray-300"></td>
                <td class="py-1 px-2 border border-gray-300  text-center"></td>
                <td class="py-1 px-2 border border-gray-300 text-center">
                  {{ formatUSNumber(Math.round(clusterTotals[cluster]?.[performanceMetric] || 0)) }}
                </td>
                <td class="py-1 px-2 border border-gray-300 text-center">
                  {{ formatUSNumber(Math.round(clusterTotals[cluster]?.UNITS || 0)) }}
                </td>
                <td class="py-1 px-2 border border-gray-300 text-center">
                  {{ formatUSNumber(Math.round(clusterTotals[cluster]?.CUSTOMER || 0)) }}
                </td>
                <td class="py-1 px-2 border border-gray-300 text-center">
                  {{ formatUSNumber(Math.round(clusterTotals[cluster]?.INVOICE || 0)) }}
                </td>
                <td class="py-1 px-2 border border-gray-300 text-center">
                  {{ formatUSNumber(Math.round(clusterTotals[cluster]?.AREA_SQFT || 0)) }}
                </td>
                <td class="py-1 px-2 border border-gray-300 text-center">
                  {{
                  formatUSNumber(Math.round(clusterTotals[cluster]?.REVENUE_PER_SQFT || 0))
                  }}
                </td>
                

              <td
                v-for="key in ethnicityKeys"
                :key="'ethnicity-' + key"
                class="py-1 px-2 border border-gray-300 text-center"
              >
                {{ formatValue(clusterTotals[cluster]?.[key], key) }}
              </td>

                  <!-- Volume contribution columns -->
                  <td
                    v-for="key in volumeKeys"
                    :key="'volume-' + key"
                    class="py-1 px-2 border border-gray-300 text-center"
                  >
                    {{ Math.round(clusterTotals[cluster]?.[key] || 0) }}%
                  </td>         
              </tr>

              <!-- Expanded cluster rows -->
              <template v-else>
                <tr v-for="store in stores" :key="store.LOC_CD" :draggable="!isDisabled" @dragstart="onDragStart($event, store)"
                  @dragover.prevent @drop="onDrop($event, cluster)" :class="['text-[13px]', !isDisabled ? 'hover:bg-green-50' : '']">
                  <td class="py-1 px-2 border border-gray-300 text-center sticky  left-0 z-10 bg-tb-back">
                    {{ store.NEW_CLUSTER_NUM }}
                  </td>
                  <td :class="['px-2 border border-gray-300 text-center sticky left-[52px] z-10 bg-tb-back', isDisabled ? 'cursor-not-allowed opacity-50' : 'cursor-move']" title="Move Stores">
                    <GripVertical class="w-4 h-4 text-gray-300" />
                  </td>
                  <td class="px-3 border border-gray-300 text-center sticky left-[83px] z-10 bg-tb-back " title="Delete Store">
                    <button @click="confirmDelete(store)" aria-label="Delete store"
                      :disabled="isDisabled"
                      :class="[isDisabled ? 'text-gray-400 cursor-not-allowed' : 'text-red-600 hover:text-red-900', 'focus:outline-none']">
                      <Trash2 class="w-3 h-3" />
                    </button>
                  </td>
                  <td class="py-1 px-2 border border-gray-300 text-center sticky left-[120px] z-10 bg-tb-back border-l-0">
                    {{ store.LOC_CD }}
                  </td>
                  <td class="py-1 px-2 border border-gray-300  text-center sticky left-[170px] z-10 bg-tb-back">
                    {{ store.LOC_NM }}
                  </td>
                  <td class="py-1 px-2 border border-gray-300 text-center">
                    {{ formatUSNumber(Math.round(store[performanceMetric] || 0)) }}
                  </td>
                  <td class="py-1 px-2 border border-gray-300 text-center">
                    {{ formatUSNumber(Math.round(store.UNITS || 0)) }}
                  </td>
                  <td class="py-1 px-2 border border-gray-300 text-center">
                    {{ formatUSNumber(Math.round(store.CUSTOMER || 0)) }}
                  </td>
                  <td class="py-1 px-2 border border-gray-300 text-center">
                    {{ formatUSNumber(Math.round(store.INVOICE || 0)) }}
                  </td>
                  <td class="py-1 px-2 border border-gray-300 text-center">
                    {{ formatUSNumber(Math.round(store.AREA_SQFT)) }}
                  </td>
                  <td class="py-1 px-2 border border-gray-300 text-center">
                    {{ formatUSNumber(Math.round(store.REVENUE_PER_SQFT)) }}
                  </td>
                  

                  <td v-for="key in ethnicityKeys" :key="'eth-val-' + key" class="py-1 px-2 border text-center">
                    {{ formatValue(store[key], key) }}
                  </td>

                  <td v-for="key in volumeKeys" :key="'vol-val-' + key" class="py-1 px-2 border text-center">
                    {{ Math.round(store[key] || 0) }}%
                  </td>       
                </tr>

                <!-- Cluster totals row -->
                <tr class="font-semibold bg-gray-200 text-[13px] border-t border-gray-300 select-none"
                  style="user-select: none" @click="toggleCluster(cluster)">
                  <td colspan="3" class="py-1 px-2 text-left sticky left-0 bg-primary ">
                    Cluster {{ cluster }} Average:
                  </td>
                  <td class="py-1 px-2 text-right sticky bg-primary left-[133px]"></td>
                  <td class="py-1 px-2 text-right sticky  bg-primary left-[175px]"></td>
                  <td class="py-1 px-2 text-center">
                    {{ formatUSNumber(Math.round(clusterTotals[cluster]?.[performanceMetric] || 0)) }}
                  </td>
                  <td class="py-1 px-2 text-center">
                    {{ formatUSNumber(Math.round(clusterTotals[cluster]?.UNITS || 0)) }}
                  </td>
                  <td class="py-1 px-2 text-center">
                    {{ formatUSNumber(Math.round(clusterTotals[cluster]?.CUSTOMER || 0)) }}
                  </td>
                  <td class="py-1 px-2 text-center">
                    {{ formatUSNumber(Math.round(clusterTotals[cluster]?.INVOICE || 0)) }}
                  </td>
                  <td class="py-1 px-2 text-center">
                    {{ formatUSNumber(Math.round(clusterTotals[cluster]?.AREA_SQFT || 0)) }}
                  </td>
                  <td class="py-1 px-2 text-center">
                    {{
                    formatUSNumber(Math.round(clusterTotals[cluster]?.REVENUE_PER_SQFT ||
                    "########"))
                    }}
                  </td>
                  
                  
                  <!-- Ethnicity totals -->
                  <td v-for="key in ethnicityKeys" :key="'eth-total-' + key" class="py-1 px-2 border text-center">
                    {{ formatValue(clusterTotals[cluster]?.[key], key) }}
                  </td>

                  <!-- Volume totals -->
                  <td v-for="key in volumeKeys" :key="'vol-total-' + key" class="py-1 px-2 border text-center">
                    {{ Math.round(clusterTotals[cluster]?.[key] || 0) }}% 
                  </td>

                  
                </tr>
              </template>
            </template>
          </tbody>
        </table>
        </div>

        <!-- Confirmation Modal -->
        <div v-if="modal.visible" class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4"
          @keydown.esc="closeModal" tabindex="-1">
          <div class="bg-white p-6 rounded shadow-lg max-w-md w-full" role="dialog" aria-modal="true"
            aria-labelledby="modal-title">
            <p id="modal-title" class="mb-4 text-gray-900">
              Are you sure you want to move store
              <strong>{{ modal.store.LOC_CD }} - {{ modal.store.LOC_NM }}</strong> from Cluster
              <strong>{{ modal.store.NEW_CLUSTER_NUM }}</strong> to Cluster
              <strong>{{ targetCluster }}</strong>?
            </p>
            <div class="flex justify-center space-x-4">
              <button @click="performMove"
                class="py-1 px-2 bg-secondary text-tx-primary rounded hover:bg-tertiary focus:outline-none focus:ring-2 focus:ring-secondary">
                Yes
              </button>
              <button @click="closeModal"
                class="py-1 px-2 bg-gray-300 rounded hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400">
                Cancel
              </button>
            </div>
          </div>
        </div>

        <!-- Delete Confirmation Modal -->
        <div v-if="deleteModal.visible"
          class="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50 p-4"
          @keydown.esc="closeDeleteModal" tabindex="-1">
          <div class="bg-white p-6 rounded shadow-lg max-w-md w-full" role="dialog" aria-modal="true"
            aria-labelledby="delete-modal-title">
            <p id="delete-modal-title" class="mb-4 text-gray-900">
              Are you sure you want to delete store
              <strong>{{ deleteModal.store.LOC_CD }}</strong> from Cluster
              <strong>{{ deleteModal.store.NEW_CLUSTER_NUM }}</strong>?
            </p>
            <div class="flex justify-center space-x-4">
              <button @click="performDelete"
                class="py-1 px-2 bg-red-600 text-tx-primary rounded hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-500">
                Delete
              </button>
              <button @click="closeDeleteModal"
                class="py-1 px-2 bg-gray-300 rounded hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400">
                Cancel
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { toRaw, onMounted, reactive, ref, computed } from "vue";
import { GripVertical, Trash2, RotateCcw } from "lucide-vue-next";
import {
  deleteStoreFromClusterAPI,
  updateStoreClustersAPI,
  getClustersAPI,
  getPreOptAPI,
  setOptimizerRunStatus,
  getOptimizerRunStatus,
  resetClustersAPI,
} from "../services/api";
import { baseFastapiURL } from '../main';

import axios from 'axios';
import { useStepsStore } from '../stores/NavigationStore.js'
import { Download, ListRestart  } from 'lucide-vue-next'
const stepsStore = useStepsStore()
const loading = ref(false);
const disableReset  = ref(true);
const isDownload = ref(false);
const props = defineProps({
  scenarioPhase: {
    type: String,
    default: ''
  }
})
const isDisabled = computed(() => props.scenarioPhase === 'completed')

const performanceMetric = computed(() => {
  return  "REVENUE"
})

const formatValue = (value, key) => {
  const val = value || 0;
  if (typeof key === 'string' && key.includes('NATIONALS')) {
    return `${Math.round(val)}%`; // no decimal
  }
  return `${Math.round(val)}%`; // remove decimal everywhere as you requested
};

function formatUSNumber(value) {
  if (value == null || value === "") return ""
  return Number(value).toLocaleString("en-US")
}
function exportData() {
  // Flatten storesByCluster into a single array
  isDownload.value = true
  const allStores = Object.entries(storesByCluster.value).flatMap(
    ([cluster, stores]) =>
      stores.map((store) => ({ ...store, CLUSTER_NUM: cluster }))
  );
  if (allStores.length === 0) {
    alert("No data to export.");
    return;
  }
  // Get all unique keys for CSV header
  const baseColumns = [
  "CLUSTER_NUM",
  "NEW_CLUSTER_NUM",
  "LOC_CD",               // Store
  "LOC_NM",               // Store Name
  performanceMetric.value,
  "UNITS",
  "CUSTOMER",
  "INVOICE",
  "AREA_SQFT",            // Area SqFt
  "REVENUE_PER_SQFT"      // Rev/SqFt
];

const formatKey = (key) => {
  switch (key) {
    case "CLUSTER_NUM": return "Cluster";
    case "NEW_CLUSTER_NUM": return "New Cluster";
    case "LOC_CD": return "Store";
    case "LOC_NM": return "Store Name";
    case "AREA_SQFT": return "Area SqFt";
    case "REVENUE_PER_SQFT": return "Rev/SqFt";
    case "CUSTOMER": return "Customers";
    case "INVOICE": return "Invoices";
    case "UNITS": return "Units";
    case "SEAC": return "SEAC";
    case "ISC": return "ISC";
    default:
      return key
        .toLowerCase()
        .split("_")
        .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
        .join(" ");
  }
};


  // Add dynamic keys (ethnicity + volume)
  const dynamicKeys = [...ethnicityKeys.value, ...volumeKeys.value];

  const finalKeys = [...baseColumns, ...dynamicKeys];
  // CSV header
  const header = finalKeys.map((key) => formatKey(key)).join(",");
  // CSV rows
  const rows = allStores.map((store) =>
    finalKeys
      .map((key) => {
        let val = store[key];
        // 1. Format REVENUE_PER_SQFT as integer
        if (key === "REVENUE_PER_SQFT") {
          val = Math.round(val ?? 0);
        }

        // 2. Format ethnicity and volume keys as rounded % string
        if ([...ethnicityKeys.value, ...volumeKeys.value].includes(key)) {
          val = `${Math.round(val ?? 0)}%`;
        }
        if (
          typeof val === "string" &&
          (val.includes(",") || val.includes('"') || val.includes("\n"))
        ) {
          val = '"' + val.replace(/"/g, '""') + '"';
        }
        return val ?? "";
      })
      .join(",")
  );
  const csvContent = [header, ...rows].join("\r\n");
  // Download
  const blob = new Blob([csvContent], { type: "text/csv" });
  const url = URL.createObjectURL(blob);
  const a = document.createElement("a");
  a.href = url;
  a.download = "store_clusters.csv";
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
  URL.revokeObjectURL(url);
  isDownload.value = false
}

const stores = ref([]);

// Group stores by cluster
const storesByCluster = computed(() => {
  const grouped = {};
  for (const store of stores.value) {
    if (!grouped[store.NEW_CLUSTER_NUM]) grouped[store.NEW_CLUSTER_NUM] = [];
    grouped[store.NEW_CLUSTER_NUM].push(store);
  }
  return grouped;
});



const clusterTotals = computed(() => {
  const totals = {};
  const fixedKeys = [
    'REVENUE',
    'UNITS',
    'CUSTOMER',
    'INVOICE',
    'AREA_SQFT',
    'REVENUE_PER_SQFT',
  ];

  // All keys to sum = fixed + ethnicity + volume
  const allKeys = [...fixedKeys, ...ethnicityKeys.value, ...volumeKeys.value];
  for (const [cluster, clusterStores] of Object.entries(storesByCluster.value)) {
    totals[cluster] = {};
    const count = clusterStores.length;

    // Initialize all keys to zero for the cluster
    for (const key of allKeys) {
      totals[cluster][key] = 0;
    }

    // Sum values for each store
    for (const store of clusterStores) {
      for (const key of allKeys) {
        const val = Number(store[key]);
        if (!isNaN(val)) {
          totals[cluster][key] += val;
        }
      }
    }

    totals[cluster].count = count;
    // Average the totals
    if (count > 0) {
      for (const key of allKeys) {
        totals[cluster][key] /= count;
        // Remove decimal from CUSTOMER key
        if (key === 'CUSTOMER') {
          totals[cluster][key] = Math.round(totals[cluster][key]);
        }
      }
    }
  }
  return totals;
});


// Expanded clusters tracking
const expandedClusters = ref(new Set());

function toggleCluster(cluster) {
  if (expandedClusters.value.has(cluster)) {
    expandedClusters.value.delete(cluster);
  } else {
    expandedClusters.value.add(cluster);
  }
}

// Drag-and-drop and modal logic
const draggedStore = ref(null);
const dragSourceCluster = ref(null);
const targetCluster = ref(null);
const pendingClusterUpdates = ref([]);

function onDragStart(event, store) {
  draggedStore.value = store;
  dragSourceCluster.value = store.NEW_CLUSTER_NUM;
  event.dataTransfer.effectAllowed = "move";
  console.log("store values  ", store);
}

function onDrop(event, newCluster) {
  if (!draggedStore.value) return;
  if (String(dragSourceCluster.value) === String(newCluster)) {
    draggedStore.value = null;
    dragSourceCluster.value = null;
    return;
  }
  targetCluster.value = newCluster;
  modal.store = draggedStore.value;
  modal.visible = true;
}

const modal = reactive({ visible: false, store: null });
function closeModal() {
  modal.visible = false;
  draggedStore.value = null;
  dragSourceCluster.value = null;
  targetCluster.value = null;
}
// When the user confirms moving a store
async function performMove() {
  if (!modal.store || targetCluster.value == null) return;

  const index = stores.value.findIndex((s) => s.LOC_CD === modal.store.LOC_CD);
  if (index === -1) return;

  // Update local store cluster immediately (optimistic UI)
  stores.value[index].NEW_CLUSTER_NUM = targetCluster.value;

  // Add to pending changes array
  const existingUpdateIndex = pendingClusterUpdates.value.findIndex(
    (u) => u.loc_cd === modal.store.LOC_CD
  );
  const cncpt_name = localStorage.getItem("concept");
  if (existingUpdateIndex !== -1) {
    // Update existing entry if any
    pendingClusterUpdates.value[existingUpdateIndex].cluster_num =
      targetCluster.value;
  } else {
    pendingClusterUpdates.value.push({
      loc_cd: modal.store.LOC_CD,
      cluster_num: targetCluster.value,
      concept: cncpt_name,
      scenario_id: sessionStorage.getItem('scenario_id'),
      territory: sessionStorage.getItem('territory_name'),
    });
  }
  sessionStorage.setItem('cluster_changed', 'true');
  disableReset.value = false;
  await setOptimizerRunStatus({
    scenario_id: sessionStorage.getItem('scenario_id'),
    run_optimizer: 0,
    run_performance: 0,
    run_range: 0
  })
  closeModal();
  await saveClusterUpdates();
}

// A new function to send all accumulated updates to backend

async function saveClusterUpdates() {
  if (pendingClusterUpdates.value.length === 0) {
    return;
  }

  try {
    const payload = {
      updates: toRaw(pendingClusterUpdates.value), // remove proxy here
    };

    await updateStoreClustersAPI(payload);

    // Clear pending updates after successful save
    pendingClusterUpdates.value = [];
    // alert("Cluster updates saved successfully!");
  } catch (error) {
    // alert("Failed to save cluster updates: " + error.message);
    // Optionally revert UI changes or refetch data
  }
}

const deleteModal = reactive({ visible: false, store: null });
function confirmDelete(store) {
  deleteModal.store = store;
  deleteModal.visible = true;
}
function closeDeleteModal() {
  deleteModal.visible = false;
  deleteModal.store = null;
}
async function performDelete() {
  if (!deleteModal.store) return;

  const { LOC_CD, CLUSTER_NUM } = deleteModal.store;
  const cncpt_name = localStorage.getItem("concept");
  const scenario_id = sessionStorage.getItem("scenario_id")
  try {
    await deleteStoreFromClusterAPI({
      loc_cd: LOC_CD,
      cluster_num: CLUSTER_NUM,
      concept: cncpt_name,
      scenario_id: scenario_id
    });

    const index = stores.value.findIndex((s) => s.LOC_CD === LOC_CD);
    if (index !== -1) {
      stores.value.splice(index, 1);
    }    
    disableReset.value = false;
  } catch (error) {
    alert(
      "Failed to delete store: " +
        (error?.detail || error?.error || "Unknown error")
    );
  }

  closeDeleteModal();
}
async function callPreFetchApi(module_name) {
  try {
    const scenario_id = sessionStorage.getItem('scenario_id');
    const response = await axios.post(`${baseFastapiURL}/run/${module_name}`, {
      SCENARIO_ID: scenario_id
    }, {
      headers: { 'Content-Type': 'application/json' }
    });
    return response.data;
  } catch (e) {
    console.error('Failed to call pre-fetch API', e);
    throw e;
  }
}
const ethnicityKeys = ref([]);
const volumeKeys = ref([]);

async function resetClusters() {
  const scenario_id = sessionStorage.getItem("scenario_id");
  const concept = localStorage.getItem("concept");
  if (!scenario_id || !concept) {
    return;
  }

  try {
    loading.value = true;

    const response = await resetClustersAPI({
      scenario_id,
      concept
    });
    await fetchClusters();
    disableReset.value = true
    console.log("Reset success:", response);
  } catch (error) {
    console.error("Reset failed:", error);
    
  } finally {
    loading.value = false;
  }
}
defineExpose({ resetClusters })

async function fetchClusters() {
  loading.value = true;
  const cncpt_name = sessionStorage.getItem('concept');
  const territory = sessionStorage.getItem('territory_name');
  const locCodes = JSON.parse(sessionStorage.getItem('loc_codes') || '[]');
  let old_cluster = sessionStorage.getItem('cluster_changed');

  if (old_cluster !== 'true') {
    sessionStorage.setItem('cluster_changed', 'false');
  }

  const scenarioData = stepsStore.getScenarioData;
  const scenario_id = scenarioData?.scenario_id || scenarioData.id;
  const eval_type = scenarioData?.eval_type;
  const loc_codes = scenarioData?.location_codes;

  try {
    const clustersData = await getClustersAPI({
      concept: cncpt_name,
      territory: territory,
      loc_codes: locCodes,
      scenario_id: scenario_id,
      eval_type: eval_type,
      loc_cd: loc_codes
    });

    const rawStores = Object.values(clustersData.data).flat();
    disableReset.value = clustersData.is_reset;

    // Extract keys
    const allEthnicityKeys = new Set();
    const allVolumeKeys = new Set();

    rawStores.forEach(store => {
      if (store.ethnicity_contribution) {
        let ethnicityData;
        if (typeof store.ethnicity_contribution === 'string') {
          try {
            ethnicityData = JSON.parse(store.ethnicity_contribution);
          } catch (e) {
            console.error('Error parsing ethnicity_contribution:', e);
            ethnicityData = {};
          }
        } else {
          ethnicityData = store.ethnicity_contribution;
        }
        Object.keys(ethnicityData).forEach(key => {
          const mappedKey = key === "EXPAT_ARAB" ? key : key;
          allEthnicityKeys.add(mappedKey);
        });
      }

      if (store.volume_contribution) {
        let volumeData;
        if (typeof store.volume_contribution === 'string') {
          try {
            volumeData = JSON.parse(store.volume_contribution);
          } catch (e) {
            console.error('Error parsing volume_contribution:', e);
            volumeData = {};
          }
        } else {
          volumeData = store.volume_contribution;
        }
        Object.keys(volumeData).forEach(key => {
          allVolumeKeys.add(key);
        });
      }
    });

    ethnicityKeys.value = Array.from(allEthnicityKeys);
    volumeKeys.value = Array.from(allVolumeKeys);

    const processed = rawStores.map((store) => {
      let ethnicityData = {};
      if (store.ethnicity_contribution) {
        if (typeof store.ethnicity_contribution === 'string') {
          try {
            ethnicityData = JSON.parse(store.ethnicity_contribution);
          } catch (e) {
            console.error('Error parsing ethnicity_contribution for store:', store.loc_cd, e);
          }
        } else {
          ethnicityData = store.ethnicity_contribution;
        }
      }

      let volumeData = {};
      if (store.volume_contribution) {
        if (typeof store.volume_contribution === 'string') {
          try {
            volumeData = JSON.parse(store.volume_contribution);
          } catch (e) {
            console.error('Error parsing volume_contribution for store:', store.loc_cd, e);
          }
        } else {
          volumeData = store.volume_contribution;
        }
      }

      const processedStore = {
        CLUSTER_NUM: store.cluster_num ?? "Unknown",
        NEW_CLUSTER_NUM: store.new_cluster_num,
        LOC_CD: store.loc_cd ?? "N/A",
        LOC_NM: store.loc_nm ?? "",
        REVENUE: Number(store.revenue) || 0,
        UNITS: Number(store.units) || 0,
        AREA_SQFT: Number(store.area_sqft) || 0,
        REVENUE_PER_SQFT: Number(store.revenue_per_sqft) || 0,
        CUSTOMER: Number(store.customers) || 0,
        INVOICE: Number(store.total_invoice) || 0,
      };

      ethnicityKeys.value.forEach(key => {
        const dataKey = key === "ARAB_EXPATS" ? "EXPAT_ARAB" : key;
        const value = ethnicityData[dataKey] || ethnicityData[key] || 0;
        processedStore[key] = Number(value);
      });

      volumeKeys.value.forEach(key => {
        const value = volumeData[key] || 0;
        processedStore[key] = Number(value);
      });

      return processedStore;
    });

    stores.value = processed;

  } catch (error) {
    console.error("Failed to fetch and process cluster data:", error);
  } finally {
    loading.value = false;
  }
}


onMounted(async () => {
  console.log('Mounted - fetching clusters');
  loading.value = true;
  const cncpt_name = sessionStorage.getItem('concept')
  const territory = sessionStorage.getItem('territory_name')
  const locCodes = JSON.parse(sessionStorage.getItem('loc_codes') || '[]')
  let old_cluster = sessionStorage.getItem('cluster_changed')
  if (old_cluster !== 'true') {
    sessionStorage.setItem('cluster_changed', 'false');
  }

  const scenarioData = stepsStore.getScenarioData;
  const scenario_id = scenarioData?.scenario_id || scenarioData.id;
  const eval_type = scenarioData?.eval_type
  const loc_codes = scenarioData?.location_codes

  try {
    await fetchClusters();
    const preOptData = await getPreOptAPI({
      concept: cncpt_name,
      territory: territory,
      loc_codes: locCodes,
      scenario_id: sessionStorage.getItem('scenario_id'),
      performance_metric: sessionStorage.getItem("performance_metric") || "REVENUE",
    });
    

    let isValid  = await getOptimizerRunStatus(scenario_id, 'runPerformance')
    if ((sessionStorage.getItem('store_config') === 'Selected Stores') && !isValid) {
      await callPreFetchApi('datapreparation');
      await callPreFetchApi('saturation');
      await setOptimizerRunStatus({
        scenario_id: sessionStorage.getItem('scenario_id'),
        run_optimizer: 0,
        run_performance: 1,
        run_range: 0
      })
    }
    loading.value = false;
  } catch (error) {
    console.error("Failed to fetch and process cluster data:", error);
    loading.value = false;
  }
});
const formatKey = (key) => {
  if (!key) return '';
  if (key === "SEAC" || key === "ISC") return key;
  return key
    .toLowerCase()
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};
</script>

<style scoped>
/* Basic fade transition for cluster expand/collapse */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.25s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}
table {
  border-spacing: 0;
  border-collapse: collapse;
}

th,
td {
  white-space: nowrap;
}
</style>
