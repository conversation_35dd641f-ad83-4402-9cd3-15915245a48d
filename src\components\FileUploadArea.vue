<template>
    <div class="group">
      <div
        :class="[
          'relative border-2 border-dashed rounded-2xl p-4 text-center transition-all duration-300 cursor-pointer overflow-hidden',
          isActive ? 'border-secondary bg-primary scale-105' : 'border-primary hover:border-secondary',
          file ? 'bg-primary border-secondary' : 'bg-white hover:bg-gray-50'
        ]"
        @drop.prevent="onDrop"
        @dragover.prevent
        @dragenter.prevent="onDragEnter"
        @dragleave="onDragLeave"
        @click="triggerFileSelect"
        :style="isDisabled ? 'pointer-events: none; opacity: 0.6;' : ''"
      >
        <div class="absolute inset-0 bg-gray-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
  
        <div class="relative z-10">
          <div :class="['w-16 h-16 mx-auto mb-2 rounded-2xl flex items-center justify-center transition-all duration-300', file ? 'bg-secondary/20' : 'bg-gray-100 group-hover:bg-primary']">
            <component :is="icon" :class="file ? 'w-8 h-8 text-tertiary' : 'w-8 h-8 text-gray-300 group-hover:text-tertiary transition-colors duration-300'" />
          </div>
  
          <div v-if="file" class="space-y-2">
            <button
            type="button"
            @click.stop="removeFile"
            title="Remove file"
            class="absolute top-2 right-2 z-20 text-red-400 hover:text-red-500 transition-colors"
            :disabled="isDisabled"
          >
            <X class="w-4 h-4" />
          </button>
          <p class="font-medium text-tx-primary-600 group-hover:text-emerald-700 transition-colors duration-300" v-html="label"></p>
            <p :class="['font-semibold text-xs truncate max-w-full', isError ? 'text-red-600' : 'text-tx-primary']">{{ file.name }}</p>
            <p v-if="!isError" class="text-sm text-secondary">Successfully uploaded</p>
            <p v-if="isError" class="text-sm text-red-600">{{ message }}</p>
            <div v-if="!isError" class="w-full bg-secondary rounded-full h-1">
              <div class="bg-secondary h-1 rounded-full w-full"></div>
            </div>
            <div v-if="isError" class="w-full bg-red-200 rounded-full h-1">
              <div class="bg-red-600 h-1 rounded-full w-full"></div>
            </div>
          </div>
          <div v-else class="space-y-2">
            <p class="font-medium text-gray-700 group-hover:text-emerald-700 transition-colors duration-300" v-html="label"></p>
            <p class="text-sm text-gray-500">Drop file here or click to browse</p>
            <p class="text-xs text-gray-300">Excel or CSV files only</p>
          </div>
        </div>
  
        <input
          :id="`file-${fileKey}`"
          ref="fileInput"
          type="file"
          class="hidden"
          accept=".csv,.xlsx"
          @change="onFileChange"
          :disabled="isDisabled"
        />
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref, computed, inject, watch } from 'vue'
  import { FileText, X } from 'lucide-vue-next'
  import axios from 'axios'
  import { useRoute } from 'vue-router'

  const props = defineProps({
    fileKey: String,
    label: String,
    icon: Object,
    scenarioPhase: {
      type: String,
      default: ''
    }
  })
  const isDisabled = computed(() => props.scenarioPhase )

  const emit = defineEmits(['upload-result'])

  const dragActive = inject('dragActive')
  const files = inject('files')
  const setDragActive = inject('setDragActive')
  const handleFileUpload = inject('handleFileUpload')

  const fileInput = ref(null)
  
  const file = computed(() => {
    const fileData = files.value[props.fileKey]
    if (!fileData) return null
    
    if (fileData.fileObject) {
      return fileData.fileObject
    }
    
    if (fileData.filename) {
      return { name: fileData.filename }
    }
    
    return null
  })
  
  const isActive = computed(() => dragActive.value === props.fileKey)
  const isLoading = ref(false)
  const message = ref('')
  const isError = ref(false)
  const route = useRoute()

  const onFileChange = async (event) => {
    const selectedFile = event.target.files[0]
    if (!selectedFile) return

    // Validate file type
const isExcel = (
  selectedFile.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
  selectedFile.name.endsWith('.xlsx')
)

const isCsv = (
  selectedFile.type === 'text/csv' ||
  selectedFile.name.endsWith('.csv')
)

if (!isExcel && !isCsv) {
  message.value = 'Invalid file type. Please upload a CSV or Excel (.xlsx) file.'
  isError.value = true
  emit('upload-result', { success: false, message: message.value })
  if (fileInput.value) {
    fileInput.value.value = ''
  }
  return
}

    // Validate file size
    if (selectedFile.size > 5 * 1024 * 1024) {
      message.value = 'File size exceeds the limit of 5MB'
      isError.value = true
      emit('upload-result', { success: false, message: message.value })
      // Clear the input value to allow reselection
      if (fileInput.value) {
        fileInput.value.value = ''
      }
      return
    }

    // Get scenario_id from URL
    const scenario_id = route.query.id
    // if (!scenario_id) {
    //   message.value = 'Error while uploading'
    //   isError.value = true
    //   emit('upload-result', { success: false, message: message.value })
    //   // Clear the input value to allow reselection
    //   if (fileInput.value) {
    //     fileInput.value.value = ''
    //   }
    //   return
    // }

    isLoading.value = true
    message.value = ''
    isError.value = false

    try {
      const formData = new FormData()
      formData.append('file', selectedFile)
      formData.append('scenario_id', scenario_id)
      formData.append('file_type', props.fileKey)

      const response = await axios.post('scenario/upload/', formData, {
        headers: { 'Content-Type': 'multipart/form-data' }
      })

      const responseData = response.data

      if (responseData.status === 'success') {
        handleFileUpload(props.fileKey, selectedFile)
        message.value = `Successfully uploaded ${responseData.file_name}`
        isError.value = false
        emit('upload-result', { success: true, message: message.value, fileId: responseData.file_id, fileType: responseData.file_type, fileName: responseData.file_name })
      } else if (responseData.status === 'error') {
        message.value = responseData.message
        isError.value = true
        handleFileUpload(props.fileKey, selectedFile) // Still show the file but with error state
        emit('upload-result', { success: false, message: message.value, fileName: responseData.file_name, fileType: responseData.file_type })
      }

    } catch (e) {
      // Handle network errors or unexpected responses
      let errorMessage = 'Error while uploading'
      
      if (e.response && e.response.data) {
        const errorData = e.response.data
        if (errorData.status === 'error' && errorData.message) {
          errorMessage = errorData.message
          handleFileUpload(props.fileKey, selectedFile) // Show file with error state
        }
      }
      
      message.value = errorMessage
      isError.value = true
      emit('upload-result', { success: false, message: message.value, fileType: props.fileKey })
    } finally {
      isLoading.value = false
      // Clear the input value after upload attempt (success or failure) to allow reselection
      if (fileInput.value) {
        fileInput.value.value = ''
      }
    }
  }
  // const removeFile = () => {
  //   if (files && props.fileKey) {
  //     files.value[props.fileKey] = null;
  //   }
  //   message.value = '';
  //   isError.value = false;
  //   if (fileInput.value) {
  //     fileInput.value.value = '';
  //   }
  //   emit('upload-result', { success: false, message: 'File removed', fileType: props.fileKey, error: false });
    
  // };


  const removeFile = async () => {
    try {
    const response = await axios.delete('scenario/deleteFile/', {
      data: {
        scenario_id: sessionStorage.getItem('scenario_id'),
        file_type: props.fileKey,
      }
    });

    if (response.data.success) {
      files.value[props.fileKey] = null;
      message.value = 'File removed';
      isError.value = false;
      if (fileInput.value) fileInput.value.value = '';

      emit('upload-result', {
        success: true,
        message: 'File removed successfully',
        fileType: props.fileKey,
        error: false
      });
    } else {
      console.error('Failed to delete file:', response.data.error);
      message.value = 'Failed to remove file';
      isError.value = true;

      emit('upload-result', {
        success: false,
        message: 'Failed to remove file',
        fileType: props.fileKey,
        error: true
      });
    }
  } catch (err) {
    console.error('Error in removeFile:', err);
    message.value = 'Error removing file';
    isError.value = true;

    emit('upload-result', {
      success: false,
      message: message.value,
      fileType: props.fileKey,
      error: true
    });
  }
};
  const triggerFileSelect = () => {
    // Clear the input value before triggering file selection to ensure change event fires
    if (fileInput.value) {
      fileInput.value.value = ''
    }
    fileInput.value?.click()
  }
  
  const onDragEnter = () => {
    setDragActive(props.fileKey)
  }
  
  const onDragLeave = () => {
    setDragActive(null)
  }
  
  const onDrop = async (event) => {
    const droppedFile = event.dataTransfer.files[0]
    if (droppedFile) {
      // Simulate the file input change event for drag and drop
      // Create a mock event object
      const mockEvent = {
        target: {
          files: [droppedFile]
        }
      }
      // Call the same validation and upload logic
      await onFileChange(mockEvent)
    }
    setDragActive(null)
  }
  </script>