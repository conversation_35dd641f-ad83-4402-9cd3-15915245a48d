<template>
  <div class="flex items-center justify-center min-h-screen bg-gradient-to-br from-green-50 to-primary">
    <div class="bg-tertiary/80 p-8 rounded-2xl shadow-2xl w-[500px] backdrop-blur-md">
      <h2 class="text-center text-tx-primary text-2xl font-semibold mb-8 tracking-wide">
        Please Select a Concept
      </h2>

      <!-- Grid for selection -->
      <div class="grid grid-cols-3 gap-4">
        <div
          v-for="concept in concepts"
          :key="concept.value"
          @click="selectedConcept = concept.value"
          class="p-5 rounded-xl border-2 cursor-pointer flex flex-col items-center justify-center transition-all duration-300 ease-out hover:scale-105"
          :class="{
            'bg-tertiary border-secondary shadow-md': selectedConcept === concept.value,
            'bg-tertiary border-transparent hover:border-secondary hover:bg-secondary':
              selectedConcept !== concept.value
          }"
        >
          <component
            :is="concept.icon"
            class="w-10 h-10 mb-3"
            :class="selectedConcept === concept.value ? 'text-tertiary' : 'text-primary'"
          />
          <span
            :class="{
              'text-tertiary font-bold': selectedConcept === concept.value,
              'text-primary font-medium': selectedConcept !== concept.value
            }"
          >
            {{ concept.label }}
          </span>
        </div>
      </div>

      <!-- Continue button -->
      <button
        class="w-full mt-8 py-3 rounded-xl text-tx-primary font-semibold text-lg shadow-lg transition-all duration-300"
        :class="selectedConcept
          ? 'bg-secondary hover:bg-tertiary active:scale-[0.98]'
          : 'bg-tertiary opacity-50 cursor-not-allowed'"
        :disabled="!selectedConcept"
        @click="handleContinue"
      >
        Continue
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref } from "vue";
import {
  BuildingOffice2Icon,
  BuildingLibraryIcon,
  BuildingStorefrontIcon
} from "@heroicons/vue/24/outline";

const selectedConcept = ref(null);

const concepts = [
  { value: "concept1", label: "Concept 1", icon: BuildingOffice2Icon },
  { value: "concept2", label: "Concept 2", icon: BuildingLibraryIcon },
  { value: "concept3", label: "Concept 3", icon: BuildingStorefrontIcon },
  { value: "concept4", label: "Concept 4", icon: BuildingLibraryIcon },
  { value: "concept5", label: "Concept 5", icon: BuildingOffice2Icon },
  { value: "concept6", label: "Concept 6", icon: BuildingStorefrontIcon }
];

const handleContinue = () => {
  if (selectedConcept.value) {
    console.log("Selected concept:", selectedConcept.value);
    // API call or router navigation goes here
  }
};
</script>
