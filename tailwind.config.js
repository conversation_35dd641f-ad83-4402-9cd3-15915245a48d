// tailwind.config.js
/** @type {import('tailwindcss').Config} */
import { addDynamicIconSelectors } from '@iconify/tailwind'
import primeui from 'tailwindcss-primeui'

export default {
  content: [
    "./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      fontFamily: {
        // Use Inter as primary body, Poppins for headings (you can tweak)
        sans: ['"Inter"', '"SFProText"', 'ui-sans-serif', 'system-ui'],
        heading: ['"Poppins"', 'ui-sans-serif', 'system-ui'],
      },
      colors: {
        primary: "#c5cdde",
        secondary: "#9ca7c1",
        tertiary: "#4a5c86",
        sidebar: "#4a5c86",
        "sidebar-hover": "#4a5c86",
        "header-bg": "#f8fafc",
        "main-bg": "#F8FAFC",
        "card-bg": "#f8fafc",

        // Text
        "tx-primary": "#FFFFFF",
        "tx-secondary": "#6B7280",

        "tb-back":"#F9FAFB",

        // Status / table
        positive: "#10B981",
        negative: "#EF4444",
        neutral: "#F59E0B",
        "table-header": "#E5F5E4",
        "table-row-hover": "#F1F5F9",
        "table-border": "#E2E8F0",
        "table-row-odd": "#FFFFFF",
        "table-row-even": "#E5F5E4",
        "group-total-bg": "#D1E7D1",
        "group-total-text": "#4E844D",

        // Full pastel family (aliases so you can use either)
        "alice-blue": "#edf2fb",
        "lavender-web": "#e2eafc",
        "lavender-web-2": "#d7e3fc",
        "periwinkle": "#ccdbfd",
        "periwinkle-2": "#c1d3fe",
        "periwinkle-3": "#b6ccfe",
        "jordy-blue": "#abc4ff",

        // Optional accent (kept commented — uncomment to use)
        // accent: "#4C63B6",
      },
      backgroundImage: {
        "gradient-top": "linear-gradient(0deg, #edf2fb, #e2eafc, #d7e3fc, #ccdbfd, #c1d3fe, #b6ccfe, #abc4ff)",
        "gradient-right": "linear-gradient(90deg, #edf2fb, #e2eafc, #d7e3fc, #ccdbfd, #c1d3fe, #b6ccfe, #abc4ff)",
        "gradient-bottom": "linear-gradient(180deg, #edf2fb, #e2eafc, #d7e3fc, #ccdbfd, #c1d3fe, #b6ccfe, #abc4ff)",
        "gradient-left": "linear-gradient(270deg, #edf2fb, #e2eafc, #d7e3fc, #ccdbfd, #c1d3fe, #b6ccfe, #abc4ff)",
        "gradient-top-right": "linear-gradient(45deg, #edf2fb, #e2eafc, #d7e3fc, #ccdbfd, #c1d3fe, #b6ccfe, #abc4ff)",
        "gradient-bottom-right": "linear-gradient(135deg, #edf2fb, #e2eafc, #d7e3fc, #ccdbfd, #c1d3fe, #b6ccfe, #abc4ff)",
        "gradient-top-left": "linear-gradient(225deg, #edf2fb, #e2eafc, #d7e3fc, #ccdbfd, #c1d3fe, #b6ccfe, #abc4ff)",
        "gradient-bottom-left": "linear-gradient(315deg, #edf2fb, #e2eafc, #d7e3fc, #ccdbfd, #c1d3fe, #b6ccfe, #abc4ff)",
        "gradient-radial": "radial-gradient(circle, #edf2fb, #e2eafc, #d7e3fc, #ccdbfd, #c1d3fe, #b6ccfe, #abc4ff)",
      },
      borderRadius: {
        'xl-2': '18px',
      },
      boxShadow: {
        'soft-lg': '0 8px 30px rgba(16,24,40,0.06)',
      }
    },
  },
  plugins: [
    primeui,
    addDynamicIconSelectors(),
    // Custom loader animation plugin
    function ({ addComponents }) {
      addComponents({
        '.tw-loader': {
          margin: '2rem',
          width: '20px',
          height: '10px',
          background: '#4a5c86',
          position: 'relative',
          animation: 'l19-0 1.5s infinite linear',
        },
        '.tw-loader:before, .tw-loader:after': {
          content: '""',
          position: 'absolute',
          bottom: '100%',
          width: '50%',
          height: '100%',
          animation: 'l19-1 1.5s infinite linear',
        },
        '.tw-loader:before': {
          left: '0',
          background: '#9ca7c1',
          '--s': '-1, 1',
        },
        '.tw-loader:after': {
          right: '0',
          background: '#afb8c5',
        },
        '@keyframes l19-0': {
          '0%, 30%': { transform: 'translateY(0) scaleY(1)' },
          '49.99%': { transform: 'translateY(-50%) scaleY(1)' },
          '50%': { transform: 'translateY(-50%) scaleY(-1)' },
          '70%, 100%': { transform: 'translateY(-100%) scaleY(-1)' },
        },
        '@keyframes l19-1': {
          '0%, 10%, 90%, 100%': { transform: 'scale(var(--s,1)) translate(0)' },
          '30%, 70%': { transform: 'scale(var(--s,1)) translate(20px)' },
          '50%': { transform: 'scale(var(--s,1)) translate(20px,20px)' },
        },
      })
    },
  ],
}
