<template>
  <div class="flex min-h-screen bg-secondary">
    <!-- Left 40%: Logo Section -->
    <div class="w-2/5 flex flex-col justify-center items-center bg-primary shadow-lg relative pb-8">
      <div class="flex items-center space-x-4 mb-4">
        <img
          :src="`${baseImgUrl}/space_logo_01.png`"
          class="h-20 bg-tertiary p-3 rounded-lg w-auto"
          alt="Space Optimisation Logo"
          style="max-width: 80%;"
        />
    <span class="text-5xl font-extrabold tracking-wider" style="color: #4a5c86;">
      SPACE LABS
    </span>

      </div>
    </div>
    <!-- Right 60%: Login Section -->
    <div class="w-3/5 flex items-center justify-center bg-tertiary">
      <div class="flex flex-col shadowCSS items-center justify-center bg-tertiary rounded-md shadow-lg w-[90%] max-w-[500px] h-[340px] relative">
        <!-- DLL Logo at the top -->
        <div class="absolute left-0 top-0 h-full w-4 rounded-l-md bg-primary"></div>
        <div class="flex justify-center items-center mt-6 mb-2 w-full z-10">
          <div class="rounded-lg p-2 flex items-center justify-center">
            <img :src="`${baseImgUrl}/dllLogo.svg`" class="h-[65px] w-15 m-0" alt="DLL Logo" />
          </div>
        </div>
        <!-- Login Content -->
        <div class="flex flex-col items-center justify-center w-full flex-1">
          <h1 class="px-4 py-2 rounded-lg text-primary font-bold mb-2">Welcome</h1>
          <p class="text-primary font-semibold text- mb-4 text-center">
            Log in to Space Labs
          </p>
          <button
            @click="signIn"
            class="flex items-center justify-center w-[80] bg-primary hover:bg-secondary text-tertiary  font-bold py-3 px-6 rounded-lg transition"
          >
            <img :src="`${baseImgUrl}/microsoft-icon.svg`" alt="Microsoft Icon" class="h-5 w-5 mr-3" />
            Sign in with Microsoft
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { redirectUri } from '../main';
import { inject } from 'vue';
const baseImgUrl = inject('baseImageUrl');

const signIn = () => {
  const clientId = '8dfe5d75-65b5-4242-8eea-5a2bfc650264';
  const tenantId = '1f9b09b4-197c-4f1c-b0c5-571a6ccc96c8';
  const scopes = `openid profile email offline_access`;

  const authorizeUrl = `https://login.microsoftonline.com/${tenantId}/oauth2/v2.0/authorize` +
    `?client_id=${clientId}` +
    `&response_type=code` +
    `&redirect_uri=${encodeURIComponent(redirectUri)}` +
    `&scope=${encodeURIComponent(scopes)}` +
    `&response_mode=query`;

  window.location.href = authorizeUrl;
};
</script>

<style scoped>
.shadowCSS {
  box-shadow: rgba(225, 231, 207, 0.5) 5px 5px 100px 0px, rgba(0, 0, 0, 0.06) 0px 0px 0px 1px;
}
</style>