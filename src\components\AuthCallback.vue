<template>
  <div>
  <PopupModal :show="showPopup" :message="popupMessage" @close="closePopup()" />
  </div>
</template>
<script setup lang="ts">
import axios from 'axios';
import { onMounted, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import {redirectUri} from '../main';
import PopupModal from './PopUpModal.vue'

const route = useRoute();
const router = useRouter();
const showPopup = ref(false);
const popupMessage = 'User is not authorized to access this application. Please contact the admin.'
onMounted(async () => {
  const code = route.query.code;

  if (!code) {
    console.error('No code found in query');
    return;
  }

  try {
    const response = await axios.post('auth/login/', {
      code,
      redirect_uri: redirectUri
    });

    const tokenData = response.data;

    // Store tokens in localStorage
    localStorage.setItem('access_token', tokenData.access_token);
    localStorage.setItem('refresh_token', tokenData.refresh_token);
    localStorage.setItem('role_id', tokenData.user.role_id);

    if (tokenData.user) {
      localStorage.setItem('user', JSON.stringify(tokenData.user.name));
      localStorage.setItem('user', JSON.stringify(tokenData.user.email));
      localStorage.setItem('concept', "hb");
    }

    // Redirect to landing page
    router.push({ name: 'HomePage' });

  } catch (error: any) {
    // Handle 401 unauthorized error with specific message
    if (error.response && error.response.status === 401) {
      const data = error.response.data;
      if (data && data.error === "User is not authorized to access this application") {
        showPopup.value = true;
        return;
      }
    }
    console.error('Token exchange failed:', error.response?.data || error.message);
  }
});
const  closePopup = () => {
  showPopup.value = false;
  router.replace('/');
};
</script>
