<script setup>
import { ref, onMounted, watch, computed, nextTick } from 'vue'
import axios from 'axios'
import dayjs from 'dayjs'
import { useRoute } from 'vue-router'
// import { API_BASE_PATH } from '../constant.js'
import DynamicFilter from './common/DynamicFilter.vue'
import * as echarts from 'echarts'
import { useStepsStore } from '../stores/NavigationStore'
import { useScenarioStore } from '../stores/ScenarioStore.js'
import { inject } from 'vue';

const route = useRoute();
const scenarioStore = useScenarioStore()
const stepsStore = useStepsStore()
const selectedStores = ref([])
const selectedGroups = ref([])
const selectedDepartments = ref([])
const selectedClasses = ref([])
const selectedSubClasses = ref([])
let subclassesInitialized = false // track initial data load
const monthRangesByLocCd = ref([])
const skipChartUpdate = ref(false)

const fromMonth = ref('')
const toMonth = ref('')
const storeOnSimilarity = ref([])

const baseImgUrl = inject('baseImageUrl');

const scenarioDetails = {
  concept: localStorage.getItem('concept'),
  scenario_id: ref(null),
  from_month: '2024-01',
  to_month: '2024-12',
  selectedMetric: ref('revenue_per_lm'),
  loc_cd: '',
  dataSummary: ref([]),
  gdcsData: ref([]),
  metricTop5: ref([]),
  metricBottom5: ref([])
}

// Watch toMonth: if September 2025, set to August 2025
const currentMonth = dayjs().format('YYYY-MM')

// watch for changes
watch(toMonth, (newVal) => {
  if (newVal === currentMonth) {
    toMonth.value = dayjs().subtract(1, 'month').format('YYYY-MM')
  }
});

const isLoading = ref(false)
const storedData = stepsStore.getScenarioData
onMounted(async () => {
  if (!fromMonth.value) {
    fromMonth.value = dayjs().subtract(6, 'month').format('YYYY-MM')
  }
  if (!toMonth.value) {
    toMonth.value = dayjs().subtract(1, 'month').format('YYYY-MM')
  }
  await getTerritoryData()
  await getGdcsData()
  await getDataSummary()
})
const getDataSummary = async () => {
  isLoading.value = true
  const cncpt_name = sessionStorage.getItem('concept') || 'hb'
  const territory_data = territory || 'AE'
  const scenarioID = sessionStorage.getItem('scenario_id')
  try {
    const response = await axios.post('scenario/getDataSummary/', {
      // concept: scenarioDetails.concept,
      // scenario_id: scenarioDetails.scenario_id.value,
      // scenario_id : storedData.scenario_id,
      scenario_id: scenarioID,
      territory_name: territory,
      concept: cncpt_name,
      group: selectedGroups.value,
      department: selectedDepartments.value,
      class_field: selectedClasses.value,
      sub_class: selectedSubClasses.value,
      from_month: fromMonth.value,
      to_month: toMonth.value,
      // territory_name: storedData.territory_name || storedData.TERRITORY_NM,
      loc_cd: selectedStores.value,
      metric: scenarioDetails.selectedMetric.value
    })
    scenarioDetails.dataSummary.value = response.data.data_points
    getMetricGraph()
  } catch (err) {
    console.error('Error fetching Data:', err)
  } finally {
    isLoading.value = false
  }
}

const formatMonth = (yyyymm) => {
  const str_yyyymm = yyyymm.toString()
  return `${str_yyyymm.slice(0, 4)}-${str_yyyymm.slice(4, 6)}`
}

const getGdcsData = async (skipDefaultStoreSelection = false) => {
  if (!skipDefaultStoreSelection) {
    isLoading.value = true
  }
  try {
    const response = await axios.post('scenario/getAllGDCSdata/', {
      concept: sessionStorage.getItem('concept') || 'hb',
      scenario_id: sessionStorage.getItem('scenario_id'),
      territory_name: territory || 'AE',
      // territory_name: storedData.territory_name || storedData.TERRITORY_NM,
    })
    scenarioDetails.gdcsData.value = response.data.gdcs_data

    monthRangesByLocCd.value = response.data.month_ranges_by_loc_cd || []
    subclassesInitialized = false; // allow one-time subclass selection on new data load
    // Initialize selectedStores with the first store from gdcs_data
    // if (!skipDefaultStoreSelection && scenarioDetails.gdcsData.value.length > 0) {
    //   selectedStores.value = [response.data.gdcs_data[0].loc_cd]
    // }
    // const formatMonth = (dateStr) => dateStr?.slice(0, 7) || ''

    // Find first store code if available
    const firstStoreCode = response.data.gdcs_data?.[0]?.loc_cd

    // Find the corresponding month range from the new response
    const storeMonthRange = response.data.month_ranges_by_loc_cd?.find(
      item => item.loc_cd === firstStoreCode
    )
    // startMonth.value = formatMonth(storeMonthRange?.start_month)
    endMonth.value = formatMonth(storeMonthRange?.end_month)
    // fromMonth.value = startMonth.value
    toMonth.value = endMonth.value

  } catch (err) {
    console.error('Error fetching Data:', err)
  } finally {
    isLoading.value = false
  }
}
const getMetricGraph = async () => {
  isLoading.value = true
  try {
    const response = await axios.post('scenario/getMetricGraph/', {
      concept: sessionStorage.getItem('concept') || 'hb',
      scenario_id: sessionStorage.getItem('scenario_id'),
      metric: scenarioDetails.selectedMetric.value,
      loc_cd: getLocCd(),
      group: selectedGroups.value,
      department: selectedDepartments.value,
      class_field: selectedClasses.value,
      sub_class: selectedSubClasses.value,
      // territory_name: storedData.territory_name || storedData.TERRITORY_NM,
      territory_name: territory,
      from_month: fromMonth.value,
      to_month: toMonth.value,
    })
    scenarioDetails.metricTop5.value = response.data.top_5
    scenarioDetails.metricBottom5.value = response.data.bottom_5
  } catch (err) {
    console.error('Error fetching Data:', err)
  } finally {
    isLoading.value = false
  }
}
const getLocCd = () => {
  let loc = selectedStores.value.length > 0 ? selectedStores.value : sessionStorage.getItem('loc_codes')

  try {
    // If sessionStorage value is JSON, parse it
    if (typeof loc === 'string' && (loc.startsWith('{') || loc.startsWith('['))) {
      loc = JSON.parse(loc)
    }
  } catch (e) {
    console.error('Invalid loc_cd in sessionStorage:', e)
    return []
  }

  // Handle array case
  if (Array.isArray(loc)) {
    return loc
  }
  
  // Handle test/control store object case
  if (loc && typeof loc === 'object') {
    if (Array.isArray(loc.testStore)) {
      return loc.testStore
    }
    return loc.testStore ? [loc.testStore] : []
  }
  if (Array.isArray(loc)) {
    return loc[0] || null
  }
  return loc || null
}

// Chart reference
const chartContainer = ref(null)
let chartInstance = null

// Computed properties for unique filter options
const uniqueStores = computed(() => {

  const stores = scenarioDetails.gdcsData.value.map(item => ({
    value: item.loc_cd,
    label: `${item.loc_cd} - ${item.loc_nm}`
  }))
  return [...new Map(stores.map(item => [item.value, item])).values()]
})

const uniqueGroups = computed(() => {
  let filtered = scenarioDetails.gdcsData.value

  if (selectedStores.value && selectedStores.value.length > 0) {
    filtered = filtered.filter(item => selectedStores.value.includes(item.loc_cd))
  }

  const groups = [...new Set(filtered.map(item => item.grp_nm))]
  return groups.map(group => ({ value: group, label: group }))
})

const uniqueDepartments = computed(() => {
  let filtered = scenarioDetails.gdcsData.value

  if (selectedStores.value && selectedStores.value.length > 0) {
    filtered = filtered.filter(item => selectedStores.value.includes(item.loc_cd))
  }
  if (selectedGroups.value.length > 0) {
    filtered = filtered.filter(item => selectedGroups.value.includes(item.grp_nm))
  }

  const departments = [...new Set(filtered.map(item => item.dpt_nm))]
  return departments.map(dept => ({ value: dept, label: dept }))
})

const uniqueClasses = computed(() => {
  let filtered = scenarioDetails.gdcsData.value

  if (selectedStores.value && selectedStores.value.length > 0) {
    filtered = filtered.filter(item => selectedStores.value.includes(item.loc_cd))
  }
  if (selectedGroups.value.length > 0) {
    filtered = filtered.filter(item => selectedGroups.value.includes(item.grp_nm))
  }
  if (selectedDepartments.value.length > 0) {
    filtered = filtered.filter(item => selectedDepartments.value.includes(item.dpt_nm))
  }

  const classes = [...new Set(filtered.map(item => item.clss_nm))]
  return classes.map(cls => ({ value: cls, label: cls }))
})

const uniqueSubClasses = computed(() => {
  let filtered = scenarioDetails.gdcsData.value

  if (selectedStores.value && selectedStores.value.length > 0) {
    filtered = filtered.filter(item => selectedStores.value.includes(item.loc_cd))
  }
  if (selectedGroups.value.length > 0) {
    filtered = filtered.filter(item => selectedGroups.value.includes(item.grp_nm))
  }
  if (selectedDepartments.value.length > 0) {
    filtered = filtered.filter(item => selectedDepartments.value.includes(item.dpt_nm))
  }
  if (selectedClasses.value.length > 0) {
    filtered = filtered.filter(item => selectedClasses.value.includes(item.clss_nm))
  }

  const subClasses = [...new Set(filtered.map(item => item.sub_clss_nm))]
  return subClasses.map(subCls => ({ value: subCls, label: subCls }))
})


// Filtered data based on selections
const filteredData = computed(() => {
  let data = scenarioDetails.dataSummary.value
  // Filter by date range
  if (fromMonth.value) {
    data = data.filter(item => item.month >= fromMonth.value.replace('-', ''))
  }
  if (toMonth.value) {
    data = data.filter(item => item.month <= toMonth.value.replace('-', ''))
  }

  return data.sort((a, b) => a.month.localeCompare(b.month))
})

// Chart data preparation
const chartData = computed(() => {
  const data = filteredData.value
  const months = data.map(item => {
    const year = item.month.substring(0, 4)
    const month = item.month.substring(4, 6)

    // Create a date and format it to "Jan 2024"
    const date = new Date(`${year}-${month}-01`)
    return date.toLocaleString('default', { month: 'short', year: 'numeric' })
  })
  const linearMeters = data.map(item => Math.round(item.metric_value * 100) / 100)
  const productivity = data.map(item => Math.round(item.total_lm * 100) / 100)

  return {
    months,
    linearMeters,
    productivity
  }
})

// Chart methods
const initChart = () => {
  if (!chartContainer.value) return

  chartInstance = echarts.init(chartContainer.value)
  updateChart()
}

const METRIC_LABEL_MAP = {
  revenue_per_lm: 'Revenue per LM',
  spd: 'Revenue per Day',
  gmv_per_lm: 'GMV per LM',
  gmv_per_day: 'GMV per Day'
}

const updateChart = () => {
  if (!chartInstance || !chartData.value.months.length) return
  const selectedMetricLabel = METRIC_LABEL_MAP[scenarioDetails.selectedMetric.value] || 'Linear Meter'
  const option = {
    // title: {
    //   text: `${selectedMetricLabel} vs LM`,
    //   left: 'center',
    //   textStyle: {
    //     fontSize: 16,
    //     fontWeight: 'bold'
    //   }
    // },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      formatter: function (params) {
        let result = `<strong>${params[0].axisValue}</strong><br/>`
        params.forEach(param => {
          result += `${param.seriesName}: ${param.value}<br/>`
        })
        return result
      }
    },
    legend: {
      data: [selectedMetricLabel, 'LM'],
      top: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: chartData.value.months,
      axisLabel: {
        formatter: function (value) {
          return value
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: selectedMetricLabel,
        position: 'left',
        axisLabel: {
          formatter: '{value}'
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed'
          }
        }
      },
      {
        type: 'value',
        name: 'LM',
        position: 'right',
        axisLabel: {
          formatter: '{value}'
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: selectedMetricLabel,
        type: 'line',
        yAxisIndex: 0,
        data: chartData.value.linearMeters,
        smooth: true,
        symbol: 'circle',
        symbolSize: 6,
        lineStyle: {
          color: '#3B82F6',
          width: 3
        },
        itemStyle: {
          color: '#3B82F6'
        },
      },
      {
        name: 'LM',
        type: 'line',
        yAxisIndex: 1,
        data: chartData.value.productivity,
        smooth: true,
        symbol: 'diamond',
        symbolSize: 6,
        lineStyle: {
          color: '#EF4444',
          width: 3
        },
        itemStyle: {
          color: '#EF4444'
        }
      }
    ]
  }

  chartInstance.setOption(option, true)
}

// Event handlers
const handleDateChange = () => {
  nextTick(() => {
    updateChart()
  })
}

const isApplyDisabled = computed(() => {
  return !selectedStores.value || !fromMonth.value || !toMonth.value
})


const clearAllFilters = () => {
  selectedStores.value = []
  selectedGroups.value = []
  selectedDepartments.value = []
  selectedClasses.value = []
  selectedSubClasses.value = []
  fromMonth.value = ''
  toMonth.value = ''
  // After next tick, select all subclasses if available
  nextTick(() => {
    if (uniqueSubClasses.value.length > 0) {
      selectedSubClasses.value = uniqueSubClasses.value[0].value // 👈 only first subclass
    }
  })
}

// Watchers
// Automatically select all subclasses at first data load OR on gdcsData change, but only if not yet initialized

watch(filteredData, () => {
  if (skipChartUpdate.value) return
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

// ✅ Reset methods
const resetOnterritoryChange = async () => {
  skipChartUpdate.value = true
  selectedStores.value = []
  selectedGroups.value = []
  selectedDepartments.value = []
  selectedClasses.value = []
  selectedSubClasses.value = []

  await getGdcsData(true)
  skipChartUpdate.value = false
}

const resetOnStoreChange = () => {
  selectedGroups.value = []
  selectedDepartments.value = []
  selectedClasses.value = []
  selectedSubClasses.value = []

  // Get the new selected store's month range
  const storeMonthRange = monthRangesByLocCd.value.find(
    item => item.loc_cd === selectedStores.value
  )

  if (storeMonthRange) {
    startMonth.value = formatMonth(storeMonthRange.start_month)
    endMonth.value = formatMonth(storeMonthRange.end_month)
    fromMonth.value = startMonth.value
    toMonth.value = endMonth.value
  }
}

const resetOnGroupChange = () => {
  selectedDepartments.value = []
  selectedClasses.value = []
  selectedSubClasses.value = []
}

const resetOnDepartmentChange = () => {
  selectedClasses.value = []
  selectedSubClasses.value = []
}

const resetOnClassChange = () => {
  selectedSubClasses.value = []
}
// Chart update on any filter change
watch([selectedGroups, selectedStores, selectedDepartments, selectedClasses, selectedSubClasses], () => {
  nextTick(() => {
    updateChart()
  })
}, { deep: true })

// Watch for route changes that might affect layout
watch(() => route.fullPath, () => {
  // Give time for the layout transition to complete
  setTimeout(() => {
    if (chartInstance) {
      chartInstance.resize()
    }
    if (topChartInstance) {
      topChartInstance.resize()
    }
    if (bottomChartInstance) {
      bottomChartInstance.resize()
    }
  }, 300) // Match the sidebar transition duration
})

// Lifecycle
onMounted(() => {
  nextTick(() => {
    initChart()

    // Create ResizeObserver to watch container size changes
    const resizeObserver = new ResizeObserver((entries) => {
      for (const entry of entries) {
        if (entry.target === chartContainer.value) {
          if (chartInstance) {
            chartInstance.resize()
          }
        }
        if (entry.target === topChartContainer.value) {
          if (topChartInstance) {
            topChartInstance.resize()
          }
        }
        if (entry.target === bottomChartContainer.value) {
          if (bottomChartInstance) {
            bottomChartInstance.resize()
          }
        }
      }
    })

    // Observe all chart containers
    if (chartContainer.value) {
      resizeObserver.observe(chartContainer.value)
    }
    if (topChartContainer.value) {
      resizeObserver.observe(topChartContainer.value)
    }
    if (bottomChartContainer.value) {
      resizeObserver.observe(bottomChartContainer.value)
    }

    // Handle window resize
    const handleResize = () => {
      if (chartInstance) {
        chartInstance.resize()
      }
      if (topChartInstance) {
        topChartInstance.resize()
      }
      if (bottomChartInstance) {
        bottomChartInstance.resize()
      }
    }

    window.addEventListener('resize', handleResize)

    // Force initial resize after a delay to ensure proper layout
    setTimeout(handleResize, 300)

    // Cleanup on unmount
    return () => {
      window.removeEventListener('resize', handleResize)
      resizeObserver.disconnect()
      if (chartInstance) {
        chartInstance.dispose()
      }
      if (topChartInstance) {
        topChartInstance.dispose()
      }
      if (bottomChartInstance) {
        bottomChartInstance.dispose()
      }
    }
  })
})


// Chart references
const topChartContainer = ref(null)
const bottomChartContainer = ref(null)
const startMonth = ref('')
const endMonth = ref('')
let topChartInstance = null
let bottomChartInstance = null
const territories = ref([])
let territory = ref('')

const getTerritoryData = async () => {
  try {
    const concept = localStorage.getItem('concept')
    const response = await axios.get('/scenario/getTerritoryData/', {
      params: { concept }
    })

    territories.value = response.data.territories.map(t => ({
      value: t.trty_short_name,
      label: t.trty_display_name,
      image: `${baseImgUrl}/${t.trty_short_name.toLowerCase()}.svg`
    }))
    console.log(territories.value)
    territory = territories.value.length > 0 ? territories.value[0].value : ''
    console.log('Selected territory:', territory)
  } catch (error) {
    console.error('Error fetching territories:', error)
    territories.value = []
  }
}

// Metric options mapping
const metricMapping = {
  'revenue_per_lm': 'Revenue / LM',
  'gmv_per_lm': 'GMV / LM',
  'spd': 'Revenue / Day',
  'gmv_per_day': 'GMV / Day'
}

const metricOptions = computed(() => {
  return Object.entries(metricMapping).map(([key, label]) => ({
    value: key,
    label: label
  }))
})

const selectedMetricLabel = computed(() => {
  return metricMapping[scenarioDetails.selectedMetric.value] || 'LM'
})

// Computed properties for chart data
const topMetricsData = computed(() => {
  const data = scenarioDetails?.metricTop5?.value
  return data.map(item => ({
    name: item.sub_class_name,
    value: Math.round(item.metric_value * 100) / 100,
    rank: item.rank
  })).sort((a, b) => b.value - a.value) // Sort by value descending
})

const bottomMetricsData = computed(() => {
  const data = scenarioDetails?.metricBottom5?.value
  return data.map(item => ({
    name: item.sub_class_name,
    value: Math.round(item.metric_value * 100) / 100,
    rank: item.rank
  })).sort((a, b) => a.value - b.value) // Sort by value ascending
})

// Summary statistics
const topPerformerValue = computed(() => {
  return topMetricsData.value.length > 0 ? topMetricsData.value[0].value : 0
})

const topPerformerName = computed(() => {
  return topMetricsData.value.length > 0 ? topMetricsData.value[0].name : 'N/A'
})

const bottomPerformerValue = computed(() => {
  return bottomMetricsData.value.length > 0 ? bottomMetricsData.value[0].value : 0
})

const bottomPerformerName = computed(() => {
  return bottomMetricsData.value.length > 0 ? bottomMetricsData.value[0].name : 'N/A'
})

const averageTop5 = computed(() => {
  if (topMetricsData.value.length === 0) return 0
  const avg = topMetricsData.value.reduce((sum, item) => sum + item.value, 0) / topMetricsData.value.length
  return Math.round(avg * 100) / 100
})

const averageBottom5 = computed(() => {
  if (bottomMetricsData.value.length === 0) return 0
  const avg = bottomMetricsData.value.reduce((sum, item) => sum + item.value, 0) / bottomMetricsData.value.length
  return Math.round(avg * 100) / 100
})

// Chart initialization and update methods
const initTopChart = () => {
  if (!topChartContainer.value) return
  topChartInstance = echarts.init(topChartContainer.value)
  updateTopChart()
}

const initBottomChart = () => {
  if (!bottomChartContainer.value) return
  bottomChartInstance = echarts.init(bottomChartContainer.value)
  updateBottomChart()
}

const updateTopChart = () => {
  if (!topChartInstance || topMetricsData.value.length === 0) return

  const option = {
    title: {
      text: 'Top 5 Performers',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: '#065f46'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params) {
        const param = params[0]
        const formattedValue = param.value.toLocaleString("en-IN", {
          maximumFractionDigits: 0
        })
        return `<strong>${param.name}</strong><br/>
                ${selectedMetricLabel.value}: ${formattedValue}<br/>
                Rank: #${param.data.rank || (param.dataIndex + 1)}`
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: topMetricsData.value.map(item => item.name),
      axisLabel: {
        rotate: 45,
        fontSize: 10,
        formatter: function (value) {
          const maxLineLength = 15;
          const words = value.split(" ");
          let result = "";
          let line = "";

          words.forEach((word) => {
            if ((line + word).length > maxLineLength) {
              result += line + "\n"; // move to next line
              line = word + " ";
            } else {
              line += word + " ";
            }
          });

          result += line.trim();
          return result;
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: true  // <-- This enables the Y-axis line
      },
      name: selectedMetricLabel.value,
      nameTextStyle: {
        fontSize: 12
      },
      splitLine: {
        show: false
      }
    },
    series: [{
      type: 'bar',
      data: topMetricsData.value.map((item, index) => ({
        value: item.value,
        rank: item.rank,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#22c55e' },
            { offset: 1, color: '#15803d' }
          ])
        }
      })),
      barWidth: '60%',
      label: {
        show: true,
        position: 'top',
        fontSize: 10,
        color: '#065f46',
        fontWeight: 'bold',
        formatter: function (params) {
          return params.value.toLocaleString("en-IN", {
            maximumFractionDigits: 0
          })
        }
      },
      emphasis: {
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#22c55e' },
            { offset: 1, color: '#15803d' }
          ])
        }
      }
    }]
  }

  topChartInstance.setOption(option, true)
}

const updateBottomChart = () => {
  if (!bottomChartInstance || bottomMetricsData.value.length === 0) return

  const option = {
    title: {
      text: 'Bottom 5 Performers',
      left: 'center',
      textStyle: {
        fontSize: 14,
        color: '#dc2626'
      }
    },
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      },
      formatter: function (params) {
        const param = params[0]
        const formattedValue = param.value.toLocaleString("en-IN", {
          maximumFractionDigits: 0
        })
        return `<strong>${param.name}</strong><br/>
              ${selectedMetricLabel.value}: ${formattedValue}<br/>
              Rank: #${param.data.rank || (param.dataIndex + 1)}`
      }
    },
    grid: {
      left: '8%',
      right: '4%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: bottomMetricsData.value.map(item => item.name),
      axisLabel: {
        rotate: 45,
        fontSize: 10,
        formatter: function (value) {
          const maxLineLength = 15;
          const words = value.split(" ");
          let result = "";
          let line = "";

          words.forEach((word) => {
            if ((line + word).length > maxLineLength) {
              result += line + "\n"; // move to next line
              line = word + " ";
            } else {
              line += word + " ";
            }
          });

          result += line.trim();
          return result;
        }
      }
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: true  // <-- This enables the Y-axis line
      },
      name: selectedMetricLabel.value,
      nameTextStyle: {
        fontSize: 12
      },
      splitLine: {
        show: false
      }
    },
    series: [{
      type: 'bar',
      data: bottomMetricsData.value.map((item) => ({
        value: item.value,
        rank: item.rank,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ef4444' }, // lighter red
            { offset: 1, color: '#991b1b' }  // darker red
          ])
        }
      })),
      barWidth: '60%',
      label: {
        show: true,
        position: 'top',
        fontSize: 10,
        color: '#dc2626',
        fontWeight: 'bold',
        formatter: function (params) {
          return params.value.toLocaleString("en-IN", {
            maximumFractionDigits: 0
          })
        }
      },
      emphasis: {
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#ef4444' },
            { offset: 1, color: '#991b1b' }
          ])
        }
      },
    }]
  }


  bottomChartInstance.setOption(option, true)
}

// Event handlers
const handleMetricChange = () => {
  // Here you would typically make an API call to fetch new data based on selected metric
  // For now, we'll just update the charts with current data
  nextTick(() => {
    updateTopChart()
    updateBottomChart()
  })
}

// Watchers
watch([topMetricsData, bottomMetricsData], () => {
  nextTick(() => {
    updateTopChart()
    updateBottomChart()
  })
}, { deep: true })

watch(scenarioDetails.selectedMetric, () => {
  // Here you would make API call with new metric
  // getMetricGraph()
  getDataSummary()
})

// Lifecycle
onMounted(() => {
  nextTick(() => {
    initTopChart()
    initBottomChart()

    // Handle window resize
    const handleResize = () => {
      if (topChartInstance) topChartInstance.resize()
      if (bottomChartInstance) bottomChartInstance.resize()
    }

    window.addEventListener('resize', handleResize)

    // Cleanup on unmount
    return () => {
      window.removeEventListener('resize', handleResize)
      if (topChartInstance) {
        topChartInstance.dispose()
        topChartInstance = null
      }
      if (bottomChartInstance) {
        bottomChartInstance.dispose()
        bottomChartInstance = null
      }
    }
  })
})

</script>

<template>
  <div class="px-6 py-2 bg-gray-50 relative">
    <!-- Loading Overlay -->
    <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-2 flex items-center justify-center">
              <div class="tw-loader"></div> 
              <div class="mr-3">Loading...</div>
            </div>
    </div>
    <div class="max-w-7xl mx-auto">
      <!-- Filters Section -->
      <div class="bg-white rounded-lg shadow-sm p-2 mb-1">

        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-5">
          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">Territory<span
                class="text-red-500 ml-1">*</span></label>
            <DynamicFilter v-model="territory" :multiselect="false" label="Territories" placeholder="Select Territories"
              :options="territories" value-key="value" label-key="label" :searchable="false"
              :close-on-select="!multiselect" variant="outline" size="sm" @change="resetOnterritoryChange()" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Store <span class="text-red-500 ml-1">*</span></label>
            <DynamicFilter v-model="selectedStores" :multiselect="true" label="Store" placeholder="Select Stores"
              :options="uniqueStores" :close-on-select="!multiselect" variant="secondary" size="sm"
              @change="resetOnStoreChange()" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Group</label>
            <DynamicFilter v-model="selectedGroups" :multiselect="true" label="Group" placeholder="Select Groups"
              :options="uniqueGroups" variant="secondary" size="sm" @change="resetOnGroupChange()" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Department</label>
            <DynamicFilter v-model="selectedDepartments" :multiselect="true" label="Department"
              placeholder="Select Department" :options="uniqueDepartments" variant="secondary" size="sm"
              @change="resetOnDepartmentChange()" />
          </div>
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Class</label>
            <DynamicFilter v-model="selectedClasses" :multiselect="true" label="Class" placeholder="Select Class"
              :options="uniqueClasses" variant="secondary" size="sm" @change="resetOnClassChange()" />
          </div>

          <!-- Second Row of Filters -->
          <div>
            <label class="ml-2 text-sm font-medium text-gray-700">Sub Class</label>
            <DynamicFilter v-model="selectedSubClasses" :multiselect="true" label="Sub Class"
              placeholder="Select Sub Class" :options="uniqueSubClasses" variant="secondary" size="sm" />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">From Month <span
                class="text-red-500 ml-1">*</span></label>
            <input v-model="fromMonth" :min="startMonth" :max="endMonth" type="month"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-secondary focus:border-secondary text-sm"
              @change="handleDateChange" />
          </div>

          <div>
            <label class="block text-sm font-medium text-gray-700 mb-1">To Month <span
                class="text-red-500 ml-1">*</span></label>
            <input v-model="toMonth" :min="startMonth" :max="endMonth" type="month"
              class="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-secondary focus:border-secondary text-sm"
              @change="handleDateChange" />
          </div>
          <!-- Filter Actions -->
          <div class="flex mt-6 gap-2">
            <button @click="clearAllFilters"
              class="px-4 h-10 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-green-500">
              Clear All
            </button>
            <button @click="getDataSummary()" :disabled="isApplyDisabled" :class="[
              'px-4 h-10 text-sm font-medium border border-transparent rounded-md focus:outline-none focus:ring-2',
              isApplyDisabled
                ? 'bg-gray-300 text-gray-600 cursor-not-allowed'
                : 'bg-tertiary hover:bg-tertiary text-tx-primary focus:ring-secondary'
            ]">
              Apply Filters
            </button>
          </div>
        </div>
      </div>
      <!-- Chart Section -->
      <div class="bg-white rounded-lg shadow-sm px-6 py-2">
        <div class="flex justify-between items-center">
          <h2 class="text-lg font-semibold text-gray-800"> Productivity over Time</h2>
          <div class="w-64">
            <DynamicFilter v-model="scenarioDetails.selectedMetric.value" :multiselect="false"
              :close-on-select="!multiselect" label="Metric" placeholder="Select Metric" :options="metricOptions"
              variant="secondary" size="sm" @change="handleMetricChange" />
          </div>
        </div>

        <div ref="chartContainer" class="w-full h-96"></div>
      </div>
    </div>
    <div class="bg-white rounded-lg shadow-sm p-6 mt-3">
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-lg font-semibold text-gray-800">Performance Metrics Comparison</h2>
      </div>

      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Top 5 Chart -->
        <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-md font-semibold text-green-800 flex items-center">
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z"
                  clip-rule="evenodd" />
              </svg>
              Top 5 Performers
            </h3>
            <span class="text-sm text-secondary font-medium">{{ selectedMetricLabel }}</span>
          </div>
          <div ref="topChartContainer" class="w-full h-80"></div>
        </div>

        <!-- Bottom 5 Chart -->
        <div class="bg-gradient-to-br from-red-50 to-rose-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-3">
            <h3 class="text-md font-semibold text-red-500 flex items-center">
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd"
                  d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v3.586L7.707 9.293a1 1 0 00-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 10.586V7z"
                  clip-rule="evenodd" />
              </svg>
              Bottom 5 Performers
            </h3>
            <span class="text-sm text-red-600 font-medium">{{ selectedMetricLabel }}</span>
          </div>
          <div ref="bottomChartContainer" class="w-full h-80"></div>
        </div>
      </div>

      <!-- Summary Stats -->
      <div class="mt-6 grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-gray-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-secondary">{{ topPerformerValue.toLocaleString("en-IN", {
            maximumFractionDigits: 0
            }) || 0 }}</div>
          <div class="text-sm text-gray-600">Best Performer</div>
          <div class="text-xs font-bold text-secondary mt-1 truncate">{{ topPerformerName }}</div>
        </div>
        <div class="bg-gray-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-secondary">{{ averageTop5.toLocaleString("en-IN", {
            maximumFractionDigits:
            0
            }) || 0 }}</div>
          <div class="text-sm text-gray-600">Top 5 Average</div>
        </div>
        <div class="bg-gray-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-red-500">{{ bottomPerformerValue.toLocaleString("en-IN", {
            maximumFractionDigits: 0
            }) || 0 }}</div>
          <div class="text-sm text-gray-600">Lowest Performer</div>
          <div class="text-xs font-bold text-red-600 mt-1 truncate">{{ bottomPerformerName }}</div>
        </div>
        <div class="bg-gray-50 rounded-lg p-4 text-center">
          <div class="text-2xl font-bold text-red-500">{{ averageBottom5.toLocaleString("en-IN", {
            maximumFractionDigits: 0 })|| 0 }}</div>
          <div class="text-sm text-gray-600">Bottom 5 Average</div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
@import "vue-multiselect/dist/vue-multiselect.css";

canvas {
  max-width: 100%;
}

/* Custom styles for the month input if needed */
input[type="month"] {
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

input[type="month"]::-webkit-calendar-picker-indicator {
  cursor: pointer;
}
</style>
