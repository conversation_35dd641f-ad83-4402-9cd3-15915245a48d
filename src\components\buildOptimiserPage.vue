<template>
  <router-view v-if="route.path !== '/BuildOptimiser'" />
  <div v-else class="">
    <!-- Step Navigation -->
    <div class="p-6 bg-secondary ">
      <div class="flex w-full gap-2 bg-[#F9FAFB] p-2 rounded-lg overflow-x-auto">
        <!-- <div class="px-3 py-1 rounded cursor-pointer flex items-center gap-1" @click="goToHome">
                    <Home class="w-4 h-4" />
                </div> -->
        <template v-for="(step, index) in stepsStore.visibleSteps" :key="index">
          <div class="px-2 py-1 rounded cursor-pointer flex items-center font-semibold text-sm" :class="{
              'bg-tertiary text-white': stepsStore.currentStep === index,
              'bg-[#F3F4F6] text-gray-300 hover:cursor-no-drop pointer-events-none cursor-not-allowed': stepsStore.currentStep !== index && step.disabled,
              'text-gray-300 pointer-events-none cursor-not-allowed': (step.name === 'Review Outliers' || step.name === 'Performance Score') && step.disabled
            }" @click="!step.disabled && handleStepClick(index)">
            {{ step.name }}
            <!-- <span v-if="step.name === 'Review Outliers' || step.name === 'Performance Score'"
              class="ml-2 px-1 py-0.3 rounded text-[0.6rem] font-medium bg-primary text-tertiary border border-tertiary">
              Optional
            </span> -->
            <span v-if="step.name === 'Review Outliers' || step.name === 'Performance Score'"
            :class="[
              'ml-2 px-1 py-0.3 rounded text-[0.6rem] font-medium border',
              step.disabled
                ? 'bg-gray-300 text-gray-600 border-gray-300'  // Gray when disabled
                : 'bg-primary text-tertiary border-tertiary' // Green when enabled
            ]">
            Optional
          </span>

          </div>
          <span v-if="index < stepsStore.visibleSteps.length - 1"
            class="text-gray-300 text-sm flex items-center">></span>
        </template>
      </div>
    </div>
    <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
          <div class="bg-white rounded-lg p-2 flex items-center justify-center">
            <div class="tw-loader"></div> 
            <div class="mr-3">Analysing and Measuring saturation levels...</div>
          </div>
    </div>

    <!-- Step Content -->
    <!-- <div class="w-full overflow-x-auto bg-[#F9FAFB] shadow rounded-lg"> -->
    <div class=" bg-[#F9FAFB] shadow rounded-lg overflow-auto">
      <!-- Use ref for ControlStoreSelection and BusinessRequirementsForm to call saveAndProceed/beforeLeave -->
      <!-- <component
        v-if="stepComponentReady"
        :is="stepsStore.activeStep.component"
        v-bind="stepsStore.activeStep.name === 'Set Test & Control' ? { ref: 'controlStoreRef' } : stepsStore.activeStep.name === 'Inputs' ? { ref: 'businessFormRef' } : {}"
      /> -->
      <BusinessDashboard v-if="stepComponentReady && stepsStore.activeStep?.name === 'Inputs'"
        ref="businessFormRef" :scenarioPhase="status" />

      <ControlStoreSelection v-if="stepComponentReady && stepsStore.activeStep?.name === 'Set Test & Control'"
        ref="controlStoreRef" :scenarioPhase="status" />
      <StoreCluster v-if="stepComponentReady && stepsStore.activeStep?.name === 'View Clusters'" :scenarioPhase="status" />
      <OptimizationSummary v-if="stepComponentReady && stepsStore.activeStep?.name === 'Optimize'" :scenarioPhase="status" />
      <OutlierHandle v-if="stepComponentReady && stepsStore.activeStep?.name === 'Review Outliers'" :scenarioPhase="status" />
      <PerformanceCalculation v-if="stepComponentReady && stepsStore.activeStep?.name === 'Performance Score'" :scenarioPhase="status" />
    </div>

    <!-- Navigation -->
    <div class="mt-4 ml-8" :class="{
        'flex justify-between': showPrev && showNext,
        'flex justify-end': !showPrev && showNext,
        'flex justify-start': showPrev && !showNext
      }">
  <div>
    <!-- Dynamic Previous Button -->
    <button
      v-if="showPrev"
      @click="saveOptimiserProgress('prev')"
      class="border border-gray-300 text-black px-4 font-semibold py-2 mb-8 rounded hover:bg-gray-100"
    >
      ← {{ previousStepName || 'Previous' }}
    </button>
  </div>
  <div>
    <!-- Jump To Optimizer button unchanged -->
    <button
      v-if="showNext && (
      (stepsStore.activeStep?.name === 'View Clusters' && storeConfigRef === 'Selected Stores') ||
      (stepsStore.activeStep?.name === 'Set Test & Control' && storeConfigRef === 'Test & Control') ||
      stepsStore.activeStep?.name === 'Review Outliers'
    )"
      @click="saveOptimiserProgress('next', true)"
      class="bg-tertiary hover:bg-tertiary text-white font-semibold mr-8 mb-8 cursor-pointer hover:bg-tertiary px-6 py-2 rounded mr-2"
      :disabled="stepsStore.currentStep === stepsStore.steps.length - 1"
    >
      Jump To Optimizer →
    </button>

    <!-- Dynamic Next Button -->
    <button
      v-if="showNext"
      @click="saveOptimiserProgress('next')"
      class="bg-tertiary hover:bg-tertiary text-white font-semibold mr-8 mb-8 cursor-pointer hover:bg-tertiary px-6 py-2 rounded mr-2"
      :disabled="stepsStore.currentStep === stepsStore.steps.length - 1"
    >
      {{ nextStepName || 'Next' }} →
    </button>
  </div>
</div>

  </div>
</template>


<script setup>
import { onMounted, ref, onUnmounted, watch, computed } from 'vue'
import { Home } from 'lucide-vue-next'
import { useRoute, useRouter } from 'vue-router'
import BusinessDashboard from './BusinessRequirementsForm.vue'
import StoreCluster from './StoreCluster.vue'
import ControlStoreSelection from './ControlStoreSelection.vue'
import SpaceDataSummary from './SpaceDataSummary.vue'
import SpaceHealthDashboard from './SpaceHealthDashboard.vue'
import EvaluationDashboard from './EvaluationDashboard.vue'
import OptimizationSummary from './Optimization.vue'
import RangeBasedOptimisationSummary from './OptimizationSummary.vue'
import SaturationPoint from './ProductivityChartsDashboard.vue'
import OutlierHandle from './OutlierHandle.vue'
import PerformanceCalculation from './PerformanceCalculation.vue'
import { useStepsStore } from '../stores/NavigationStore.js'
import { updateScenarioStatus, setOptimizerRunStatus, getOptimizerRunStatus} from '../services/api.js'
import axios from 'axios'
import { baseFastapiURL } from '../main';

// --- Watch sessionStorage store_config and call onConfigurationSelect ---
import { watchEffect } from 'vue'
const isLoading = ref(false)
const storeConfigRef = ref(sessionStorage.getItem('store_config'))
let lastStoreConfig = storeConfigRef.value
setInterval(() => {
  const current = sessionStorage.getItem('store_config')
  if (current !== lastStoreConfig) {
    lastStoreConfig = current
    storeConfigRef.value = current
  }
}, 300)

watch(storeConfigRef, (val, oldVal) => {
  if (val && val !== oldVal) {
    onConfigurationSelect(val)
  }
})

const stepsStore = useStepsStore()
const currentStep = ref(0)
const router = useRouter()
const route = useRoute()
const stepComponentReady = ref(false)

const handleStepClick = async (clickedIndex) => {
  // Determine if we're moving forward or backward
  const action = clickedIndex > stepsStore.currentStep ? 'next' : 'prev'
  // Save progress before navigating
  const canNavigate = await saveOptimiserProgress(action)
  if (canNavigate === false) return;
  // Then navigate to the clicked step
  stepsStore.goToStep(clickedIndex)
}

onUnmounted(() => {
  stepsStore.clearScenarioData()
  sessionStorage.clear()
})
const noNextButtons = ['Optimize']
const noPrevButtons = ['Inputs', 'Optimize']

const showPrev = computed(() => !noPrevButtons.includes(stepsStore.activeStep.name))
const showNext = computed(() => !noNextButtons.includes(stepsStore.activeStep.name))
const goToHome = () => {
  router.push({ name: '/spaceoptimization/HomePage' })
}

const previousStepName = computed(() => {
  const storeConfig = sessionStorage.getItem("store_config");
  let buttonNames = []
  if (storeConfig === 'Selected Stores') {
    buttonNames = ['Inputs','Cluster', 'Outlier', 'Performance'];
  } else {
    buttonNames = ['Inputs', 'Cluster', 'Test & Control', 'Outlier', 'Performance'];
  }
  const prevIndex = stepsStore.currentStep - 1;
  if (prevIndex >= 0) {
    return buttonNames[prevIndex] || null;
  }
  return null;
});

const nextStepName = computed(() => {
  const currentStepIndex = stepsStore.currentStep;
  const nextIndex = stepsStore.currentStep + 1;
  const storeConfig = sessionStorage.getItem("store_config");
  let buttonNames = []
  if (storeConfig === 'Selected Stores') {
    buttonNames = ['Cluster', 'Outlier', 'Performance', 'Optimize'];
  } else {
    buttonNames = ['Cluster', 'Test & Control', 'Outlier', 'Performance', 'Optimize'];
  }
  if (nextIndex < buttonNames.length+1) {
    return buttonNames[nextIndex-1] || null;
  }
  return null;
});
const status = route.query.status
const scenario_id = null
onMounted(async () => {
  const id = route.query.id
  const finishSetup = () => { stepComponentReady.value = true }
  if (id) {
    // If id is present in query, use it (do not create new)
    if (status === 'update' || status === 'completed') {
      await getOptimizer(id)
      await onConfigurationSelect(sessionStorage.getItem('store_config'))
      finishSetup()
    } else if (status === 'create') {
      stepsStore.steps.forEach((step, index) => {
        if (step.name === 'Set Test & Control') {
          step.hidden = true
          step.disabled = true
        } else {
          step.disabled = index !== 0
          step.hidden = false
        }
      })
      stepsStore.currentStep = 0
      stepsStore.clearScenarioData()
      await getOptimizer(id)
      await onConfigurationSelect(sessionStorage.getItem('store_config'))
      finishSetup()
    } else {
      finishSetup()
    }
    return
  } else if (status === 'create') {
    // No id, so create new
    stepsStore.steps.forEach((step, index) => {
      if (step.name === 'Set Test & Control') {
    step.hidden = true
    step.disabled = true
  } else {
    step.disabled = index !== 0
    step.hidden = false
  }
    })
    stepsStore.currentStep = 0
    stepsStore.clearScenarioData()
    await createNewOptimiser()
    finishSetup()
    return
  } else if (status === 'update') {
    await getOptimizer(id)
    finishSetup()
  } else {
    router.push({ name: 'HomePage' })
  }
})
const onConfigurationSelect = async (value) => {
  if (value === 'Selected Stores') {
    stepsStore.setStepVisible('View Clusters', false)
    stepsStore.setStepVisible('Set Test & Control', true)
  }
  if (value === 'Set Test & Control') {
    stepsStore.setStepVisible('Set Test & Control', false)
  }
  if (value === null) {
    stepsStore.setStepVisible('Set Test & Control', true)
    stepsStore.setStepVisible('View Clusters', true)
  }
}
const data = ref({})
const getOptimizer = async (id) => {
  try {
    const response = await axios.post('/scenario/getOptimizerDetails/', {
      "scenario_id": id
    })
    data.value = response.data.data
    stepsStore.setScenarioData(data.value)
    // Set sessionStorage as before
    sessionStorage.setItem('scenario_id', data.value.id)
    sessionStorage.setItem('concept', data.value.CNCPT_NM)
    sessionStorage.setItem('territory_name', data.value.TERRITORY_NM)
    sessionStorage.setItem('performance_metric', data.value.metric)
    sessionStorage.setItem('weights', JSON.stringify(data.value.weights))
    sessionStorage.setItem('store_config', data.value.eval_type)
    if (data.value.eval_type === 'Test & Control') {
      sessionStorage.setItem('testControlMappings', data.value.loc_cd)
      const parsedData = JSON.parse(data.value.loc_cd)
      const locCodes = parsedData.flatMap(item => [
        item.testStore,
        ...(item.controlStores || [])
      ])
      sessionStorage.setItem('loc_codes', JSON.stringify(locCodes))
      stepsStore.setStepVisible('Set Test & Control', true)
    } else {
      sessionStorage.setItem('loc_codes', data.value.loc_cd)
      stepsStore.setStepVisible('Set Test & Control', false)
    }

    // Enable all steps up to progress_page-1, disable after
    stepsStore.steps.forEach((step, idx) => {
      if(data.value.eval_type == 'Test & Control'){
      step.disabled = idx > (data.value.progress_page)
      step.hidden = false
      }
      else{
        step.disabled = idx > (data.value.progress_page+1)
        step.hidden = false
      }
    })
    // Set current step to current_page-1 (0-based index)
    stepsStore.currentStep = Math.max(0, (data.value.current_page || 0))
  } catch (err) {
    console.error('Error fetching data:', err)
  }
}
  async function callPreFetchApi(module_name) {
  isLoading.value = true;
  try {
    const scenario_id = sessionStorage.getItem('scenario_id');
    const response = await axios.post(`${baseFastapiURL}/run/${module_name}`, {
      SCENARIO_ID: scenario_id
    }, {
      headers: { 'Content-Type': 'application/json' }
    });
    isLoading.value = false;
    return response.data;
  } catch (e) {
    console.error('Failed to call pre-fetch API', e);
    isLoading.value = false;
    throw e;
  }
}
const runPerformance = async () => {
  try {
    let valid = await getOptimizerRunStatus(sessionStorage.getItem('scenario_id'), 'runPerformance');
    if (!valid) {
      await callPreFetchApi('datapreparation');
      await callPreFetchApi('saturation');
      await setOptimizerRunStatus({
        scenario_id: sessionStorage.getItem('scenario_id'),
        run_performance: 1,
        run_optimizer: 0,
        run_range: 0,
      });
      return true;
    }
    if (valid) {
      return true;
    }
    return false;
  } catch (e) {
    console.error('runPerformance failed', e);
    return false;
  }
};
const createNewOptimiser = async () => {
  try {
    const response = await axios.post('/scenario/createOptimizer/')
    console.log(response, 'response')
    if (response && response.data.id) {
      sessionStorage.setItem('scenario_id', response.data.id)
      router.replace({
        query: {
          ...route.query,
          id: response.data.id,
          status: 'create'

        }
      })
    }
  } catch (err) {
    console.error('Error creating new optimizer:', err)
  }
}

const businessFormRef = ref(null);
const controlStoreRef = ref(null);

const saveOptimiserProgress = async (action, optJump = false) => {
  try {
    const evalTypeValue = sessionStorage.getItem('store_config')
    let progressPage = stepsStore.currentStep;
    let jumpStep = null;
    // Await save in BusinessRequirementsForm before moving next
    if (action === 'next' && stepsStore.activeStep?.name === 'Inputs') {
      if (businessFormRef.value && businessFormRef.value.beforeLeave) {
        const success = await businessFormRef.value.beforeLeave();
        if (success === undefined || success === false) return false; // Do not proceed if validation failed
      }
    }
    // Await save in ControlStoreSelection before moving next
    if (action === 'next' && stepsStore.activeStep?.name === 'Set Test & Control') {
      // if (controlStoreRef.value && controlStoreRef.value.saveAndProceed) {
      const success = await controlStoreRef.value.saveAndProceed();
      if (!success) return false; // Do not proceed if save failed
      const perf = await runPerformance();
      if (perf === false) return false; // Do not proceed if runPerformance failed
      // }
    }
    if (
      action === 'next' && stepsStore.activeStep?.name === 'View Clusters' && evalTypeValue === 'Selected Stores' && optJump) {
      let cluster_changed = sessionStorage.getItem('cluster_changed') == 'true' ? true : false;
        if (cluster_changed) {
          await runPerformance();
        sessionStorage.setItem('cluster_changed', false);
        }
        progressPage = stepsStore.currentStep + 3;
        jumpStep = 4;
    }
    if (
      action === 'next' && stepsStore.activeStep?.name === 'Set Test & Control' && evalTypeValue === 'Test & Control' && optJump
    ) {
      progressPage = stepsStore.currentStep + 3;
      jumpStep = 5;
    }
    if (action === 'next' && stepsStore.activeStep?.name === 'Review Outliers' && optJump) {
      progressPage = stepsStore.currentStep + 2;
      jumpStep = stepsStore.currentStep + 2;
    }
    await updateScenarioStatus({
      scenario_id: sessionStorage.getItem('scenario_id'),
      current_page: stepsStore.currentStep,
      progress_page: progressPage
    })
    if (action === 'next') {
      if (jumpStep !== null) {
        stepsStore.goToStep(jumpStep)
      } else {
        stepsStore.goToNextStep()
      }
    } else if (action === 'prev') {
      stepsStore.goToPreviousStep()
    }
    return true;
  } catch (err) {
    console.error('Error updating scenario status:', err)
    return false;
  }
}

// Track last step to determine direction
let lastStep = stepsStore.currentStep
// watch(
//   () => stepsStore.currentStep,
//   async (newStep, oldStep) => {
//     if (newStep !== oldStep) {
//       // Determine direction
//       const action = newStep > oldStep ? 'next' : 'prev'
//       await saveOptimiserProgress(action)
//     }
//     lastStep = newStep
//   }
// )
</script>

<style scoped>
/* Optional scroll behavior */
::-webkit-scrollbar {
  height: 6px;
}

::-webkit-scrollbar-thumb {
  background-color: #ccc;
  border-radius: 4px;
}
</style>