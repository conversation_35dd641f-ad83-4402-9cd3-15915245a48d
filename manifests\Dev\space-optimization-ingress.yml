apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: radar-fastapi-ingress
  namespace: dll-radar
  annotations:
    nginx.ingress.kubernetes.io/backend-protocol: "HTTP"
    nginx.ingress.kubernetes.io/use-regex: "true"
    nginx.ingress.kubernetes.io/rewrite-target: /$2
    nginx.ingress.kubernetes.io/proxy-connect-timeout: "2500"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "2500"  
    nginx.ingress.kubernetes.io/proxy-read-timeout: "2500"
    nginx.ingress.kubernetes.io/proxy-body-size: "250m"
spec:
  ingressClassName: nginx
  rules:
    - host: lmdllcloudapp-dev.landmarkgroup.com
      http:
        paths:    
          - path: /radar_openai/fastapi(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: radar-fastapi-backend-service
                port:
                  number: 80