<script setup lang="ts">
import { ref } from 'vue';
import axios from 'axios';
import Multiselect from 'vue-multiselect';
import { UserPlus, Network, Edit, ToggleLeft, ToggleRight } from 'lucide-vue-next';

// Data
const roles = ['HOP', 'HOR', 'HOF', 'Planning analyst'];
const concepts = ['Babyshop', 'Homebox', 'Lifestyle', 'Splash'];
const territories = ['UAE', 'KSA', 'QATAR', 'OMAN'];
const gdcsLevels = ['Group', 'Department', 'Class', 'Subclass'];

// State
const users = ref([]);
const mappings = ref([]);
const activeTab = ref<'users' | 'concepts'>('users');

// Popups
const showCreateUser = ref(false);
const showEditPopup = ref(false);
const showConceptMapping = ref(false);
const showEditMappingPopup = ref(false);

// Forms
const newUser = ref({ email: '', role: '', concept: '', available: true });
const editingUser = ref(null);
const emailError = ref('');

const conceptMapping = ref({ concept: '', territories: [], gdcs: '' });
const editingMapping = ref(null);

// User logic
function addUser() {
  if (!/^[^@]+@[^@]+\.(com)$/.test(newUser.value.email)) {
    emailError.value = 'Email must end with .com';
    return;
  }
  if (!newUser.value.concept) {
    emailError.value = 'Please select a concept';
    return;
  }
  users.value.push({ ...newUser.value });
    newUser.value = { email: '', role: '', concept: '', available: true };
    emailError.value = '';
    showCreateUser.value = false;
}
async function addNewUser() {
  try {
    const response = await axios.post(
      '/user-dashboard/addUser/',
      newUser
    );
    const data = response.data;
    console.log(data)
    users.value.push({ ...newUser.value });
    newUser.value = { email: '', role: '', concept: '', available: true };
    emailError.value = '';
    showCreateUser.value = false;
  } catch (error: any) {
    throw error.response?.data || error.message;
  }
}

function editUser(user) {
  editingUser.value = { ...user };
  showEditPopup.value = true;
}

function saveEditUser() {
  const idx = users.value.findIndex(u => u.email === editingUser.value.email);
  if (idx !== -1) users.value[idx] = { ...editingUser.value };
  showEditPopup.value = false;
}

function toggleAvailable(user) {
  user.available = !user.available;
}

// Concept mapping logic
function addMapping() {
  if (!conceptMapping.value.concept || !conceptMapping.value.territories.length || !conceptMapping.value.gdcs) return;
  mappings.value.push({ ...conceptMapping.value });
  conceptMapping.value = { concept: '', territories: [], gdcs: '' };
  showConceptMapping.value = false;
}

function editMapping(mapping) {
  editingMapping.value = { ...mapping };
  showEditMappingPopup.value = true;
}

function saveEditMapping() {
  const idx = mappings.value.findIndex(
    m => m.concept === editingMapping.value.concept && m.gdcs === editingMapping.value.gdcs
  );
  if (idx !== -1) mappings.value[idx] = { ...editingMapping.value };
  showEditMappingPopup.value = false;
}
</script>

<template>
  <div class="p-8 min-h-screen">
    <!-- Top Buttons -->
    <div class="flex gap-4 mb-8">
      <button @click="showCreateUser = true"
        class="flex items-center gap-2 px-6 py-2 rounded-lg font-semibold shadow shadow-secondary/25 transition bg-white/80 text-tertiary hover:bg-primary">
        <UserPlus class="w-5 h-5" /> Create User
      </button>
      <button @click="showConceptMapping = true"
        class="flex items-center gap-2 px-6 py-2 rounded-lg font-semibold shadow shadow-secondary/25 transition bg-white/80 text-tertiary hover:bg-primary">
        <Network class="w-5 h-5" /> Add Concept Mapping
      </button>
    </div>

    <div class="bg-white rounded-2xl p-8 shadow-lg shadow-gray-300/25 w-auto mx-auto mb-8 mt-4">
      <!-- Toggle Tabs -->
      <div class="flex items-center gap-2 mb-6">
        <button
          @click="activeTab = 'users'"
          :class="activeTab === 'users' ? 'border-b-2 border-secondary text-tertiary' : 'text-tertiary hover:border-b-2 hover:border-secondary'"
          class="bg-transparent px-4 py-2 font-semibold transition rounded"
        >
          All Users
        </button>
        <button
          @click="activeTab = 'concepts'"
          :class="activeTab === 'concepts' ? 'border-b-2 border-secondary text-tertiary' : 'text-tertiary hover:border-b-2 hover:border-secondary'"
          class="bg-transparent px-4 py-2 font-semibold transition rounded"
        >
          Concept Mapping
        </button>
        <span class="ml-auto text-sm text-tertiary font-semibold" v-if="activeTab === 'users'">
          Total Users: {{ users.length }}
        </span>
      </div>

      <!-- User Table -->
      <table v-if="activeTab === 'users'" class="w-full text-sm bg-white rounded shadow mb-6">
        <thead class="bg-white py-3">
          <tr class="text-left text-tx-primary">
            <th class="py-1 px-4">Username</th>
            <th class="py-1">Email</th>
            <th class="py-1">Role</th>
            <th class="py-1">Concept</th>
            <th class="py-1">Available</th>
            <th class="py-1"></th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="user in users" :key="user.email" class="hover:bg-primary transition">
            <td class="py-1 px-4">{{ user.email.split('@')[0] }}</td>
            <td class="py-1">{{ user.email }}</td>
            <td class="py-1">{{ user.role }}</td>
            <td class="py-1">{{ user.concept }}</td>
            <td class="py-1">
              <button @click="toggleAvailable(user)" class="focus:outline-none">
                <component :is="user.available ? ToggleRight : ToggleLeft"
                  :class="user.available ? 'text-secondary' : 'text-gray-300'" class="w-6 h-6" />
              </button>
            </td>
            <td class="py-1">
              <button @click="editUser(user)"
                class="text-emerald-700 underline flex items-center gap-1 text-xs">
                <Edit class="w-4 h-4" /> Edit
              </button>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- Concept Mapping Table -->
      <div v-if="activeTab === 'concepts'" class="w-full text-sm bg-white rounded shadow mb-6">
        <table class="w-full bg-white rounded shadow">
          <thead>
            <tr class="bg-primary/50 text-left text-tertiary">
              <th class="py-1 px-4">Concept</th>
              <th class="py-1">Territories</th>
              <th class="py-1">GDCS Level</th>
              <th class="py-1"></th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="mapping in mappings" :key="mapping.concept + mapping.gdcs"
              class="hover:bg-primary transition">
              <td class="py-1 px-4">{{ mapping.concept }}</td>
              <td class="py-1">{{ mapping.territories.join(', ') }}</td>
              <td class="py-1">{{ mapping.gdcs }}</td>
              <td class="py-1">
                <button @click="editMapping(mapping)"
                  class="text-tertiary underline flex items-center gap-1 text-xs">
                  <Edit class="w-4 h-4" /> Edit
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Edit User Popup -->
      <div v-if="showEditPopup"
        class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
        <div class="bg-white p-6 rounded-2xl shadow-lg shadow-secondary/25 w-80">
          <h4 class="font-semibold mb-2 text-tertiary">Edit User</h4>
          <multiselect v-model="editingUser.role" :options="roles" placeholder="Select Role"
            :show-labels="false" :close-on-select="true" class="mb-2 text-sm" />
          <button @click="saveEditUser"
            class="bg-gradient-to-r from-secondary/80 to-tertiary text-tx-primary px-4 py-2 rounded font-semibold mr-2 shadow shadow-primary text-sm hover:from-tertiary hover:to-secondary/80 transition">
            Save
          </button>
          <button @click="showEditPopup = false"
            class="bg-secondary/10 text-tertiary px-4 py-2 rounded font-semibold text-sm">
            Cancel
          </button>
        </div>
      </div>
    </div>

    <!-- Create User Popup -->
    <div v-if="showCreateUser" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
      <div class="bg-white rounded-xl shadow-lg w-full max-w-md">
        <div class="p-6 border-b border-gray-200 bg-primary rounded-t-lg">
          <h3 class="text-lg font-semibold">Create New User</h3>
        </div>
        <div class="p-6 text-sm">
          <div class="mb-4 w-3/4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Email *</label>
            <input
              v-model="newUser.email"
              type="email"
              class="w-full h-10 px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-secondary focus:border-secondary"
              placeholder="Enter email"
            />
            <div v-if="emailError" class="text-red-500 text-xs mt-1">{{ emailError }}</div>
          </div>
          <div class="mb-4 w-3/4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Role *</label>
            <multiselect
              v-model="newUser.role"
              :options="roles"
              placeholder="Select Role"
              class="mb-2 text-sm h-10"
              :show-labels="false"
              :close-on-select="true"
            />
          </div>
          <div class="mb-4 w-3/4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Concept *</label>
            <multiselect
              v-model="newUser.concept"
              :options="concepts"
              placeholder="Select Concept"
              :show-labels="false"
              :close-on-select="true"
              class="mb-2 text-sm h-10"
            />
          </div>
          <div class="flex justify-end gap-2 mt-6">
            <button @click="addUser"
              class="flex items-center bg-gradient-to-r from-secondary/80 to-tertiary text-tx-primary px-4 py-2 rounded font-semibold shadow shadow-secondary/25 hover:from-tertiary hover:to-secondary/80 transition text-sm">
              <UserPlus class="h-4 w-4" />
              <span>Create User</span>
            </button>
            <button @click="showCreateUser = false"
              class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Concept Mapping Popup -->
    <div v-if="showConceptMapping" class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-30">
      <div class="bg-white rounded-xl shadow-lg w-full max-w-md">
        <div class="p-6 border-b bg-primary rounded-t-lg border-gray-200">
          <h3 class="text-lg font-semibold text-tx-primary">Add Concept Mapping</h3>
        </div>
        <div class="p-6">
          <div class="mb-4 w-3/4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Concept *</label>
            <multiselect
              v-model="conceptMapping.concept"
              :options="concepts"
              open-direction="bottom"
              placeholder="Select Concept"
              :show-labels="false"
              :close-on-select="true"
              class="mb-2 h-10 text-sm"
            />
          </div>
          <div class="mb-4 w-3/4">
            <label class="block text-sm font-medium text-gray-700 mb-2">Territories *</label>
            <multiselect
              v-model="conceptMapping.territories"
              :options="territories"
              open-direction="bottom"
              placeholder="Select Territories"
              :multiple="true"
              :show-labels="false"
              :close-on-select="false"
              class="mb-2 text-sm h-10"
            >
            </multiselect>
          </div>
          <div class="mb-4 w-3/4">
            <label class="block text-sm font-medium text-gray-700 mb-2">GDCS Level *</label>
            <multiselect
              v-model="conceptMapping.gdcs"
              :options="gdcsLevels"
              open-direction="bottom"
              placeholder="Select GDCS Level"
              :show-labels="false"
              :close-on-select="true"
              class="mb-2 text-sm h-10"
            />
          </div>
          <div class="flex justify-end gap-2 mt-6">
            <button @click="addMapping"
              class="flex items-center bg-gradient-to-r from-secondary/80 to-tertiary text-tx-primary px-4 py-2 rounded font-semibold shadow shadow-secondary/25 hover:from-tertiary hover:to-secondary/80 transition text-sm">
              <Network class="h-4 w-4" />
              <span>Add Mapping</span>
            </button>
            <button @click="showConceptMapping = false"
              class="px-4 py-2 bg-gray-200 text-gray-700 rounded-md hover:bg-gray-300">
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Edit Mapping Popup -->
    <div v-if="showEditMappingPopup"
      class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
      <div class="bg-white p-6 rounded-2xl shadow-lg shadow-secondary/25 w-80">
        <h4 class="font-semibold mb-2 text-tertiary">Edit Mapping</h4>
        <multiselect v-model="editingMapping.concept" :options="concepts" placeholder="Select Concept"
          :show-labels="false" :close-on-select="true" class="mb-2 text-sm" />
        <multiselect v-model="editingMapping.territories" :options="territories"
          placeholder="Select Territories" :multiple="true" :show-labels="false" :close-on-select="false"
          class="mb-2 text-sm" />
        <multiselect v-model="editingMapping.gdcs" :options="gdcsLevels" placeholder="Select GDCS Level"
          :show-labels="false" :close-on-select="true" class="mb-2 text-sm" />
        <button @click="saveEditMapping"
          class="bg-gradient-to-r from-secondary/80 to-tertiary text-tx-primary px-4 py-2 rounded font-semibold mr-2 shadow shadow-secondary/25 text-sm hover:from-tertiary hover:to-secondary/80 transition">
          Save
        </button>
        <button @click="showEditMappingPopup = false"
          class="bg-secondary/10 text-tertiary px-4 py-2 rounded font-semibold text-sm">
          Cancel
        </button>
      </div>
    </div>
  </div>
</template>

<style src="vue-multiselect/dist/vue-multiselect.min.css"></style>