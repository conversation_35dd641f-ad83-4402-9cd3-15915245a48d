<template>
  <div v-if="show" class="fixed inset-0 bg-black bg-opacity-40 z-50 flex items-center justify-center">
    <div class="bg-white rounded-lg shadow-lg w-full max-w-md p-6">
      <div class="text-center">
        <h2 class="text-lg font-semibold text-gray-800 mb-4">Alert</h2>
        <p class="text-sm text-gray-600" v-html="message"></p>
        <div class="mt-6">
          <button
            @click="close"
            class="bg-secondary text-tx-primary px-6 py-2 rounded hover:bg-tertiary transition"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  show: Boolean,
  message: String
})

const emit = defineEmits(['close'])

const close = () => {
  emit('close')
}
</script>

<style scoped>
/* Optional: Add animation or transition */
</style>
