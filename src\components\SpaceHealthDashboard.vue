<script setup lang="ts">
import { ref, onMounted,computed } from 'vue'
import axios from 'axios'
import SpaceHealthMetricsTable from './SpaceHealthMetricsTable.vue'
import SpaceHealthFilterBar from './SpaceHealthFilterBar.vue'
import { useStepsStore } from '../stores/NavigationStore.js'
import dayjs from "dayjs"

const stepsStore = useStepsStore()
const currentScenario = computed(() => stepsStore.getScenarioData)
// Scenario and table state
const scenarioDetails = ref({
  gdcsData: [],
  concept: '',
  scenario_id: null
})

const filters = ref<{ [key: string]: string[] }>({})
const tableData = ref([])
const summaryTotals = ref([])
const currentPage = ref(1)
const pageSize = ref(10)
const totalRows = ref(0)
const isLoading = ref(false)
const totalPages = ref(0)
const nextPageNum = ref<number | null>(null)
const previousPageNum = ref<number | null>(null)
const monthRangesByLocCd = ref([])
const startMonth = ref('')
const endMonth = ref('')
const selectedTerritory = ref<string | null>(null)
// Map API row to table row
const mapApiRowToTableRow = (item) => ({
  locCode: item.loc_cd,
  locNm : item.loc_nm,
  group: item.grp_nm,
  department: item.dpt_nm,
  class: item.clss_nm,
  subClass: item.sub_clss_nm,
  sqft: item.sqft,
  lm: item.lm,
  lmCont: item.lm_contribution_in_store,
  optionCount: item.option_count,
  diff: item.diff_lm_sqft_contrib,
  optionDensity: item.option_density,
  sohQty: item.soh,
  invCount: item.mnth_end_soh,
  stockDensity: item.stock_density,
  revenue: item.rev,
  gmv: item.gmv,
  ros: item.ros,
  cover: item.cover,
  revenuePerDay: item.rev_per_day,
  gmvPerDay: item.gmv_per_day,
  revPerLmPerDay: item.rev_per_lm_per_day,
  gmvPerLmPerDay: item.gmv_per_lm_per_day,
  revPerSqftPerDay: item.rev_per_sqft_per_day,
  gmvPerSqftPerDay: item.gmv_per_sqft_per_day,
  lmRank: item.lm_rank,
  sqftRank: item.sqft_rank,
  revPerLmPerDayRank: item.rev_per_lm_per_day_rank,
  gmvPerLmPerDayRank: item.gmv_per_lm_per_day_rank,
  revPerLM: item.rev_per_lm,
  gmvPerLM: item.gmv_per_lm,
  revPerSqft: item.rev_per_sqft,
  gmvPerSqft: item.gmv_per_sqft,
  revPerSqftPerDayRank: item.rev_per_sqft_per_day_rank,
  gmvPerSqftPerDayRank: item.gmv_per_sqft_per_day_rank,
  fixtureDensity: item.fixture_density,
  sqftCont: item.sqft_contribution_in_store,
  diffLmSqftCont: item.diff_lm_sqft_contrib,
  summaryLevel: item.summary_level,
  gmroi: item.gmroi
})
const formatMonth = (yyyymm, subtractMonths = 0) => {
  const str_yyyymm = yyyymm.toString()
  const date = dayjs(`${str_yyyymm.slice(0, 4)}-${str_yyyymm.slice(4, 6)}-01`)
  return date.subtract(subtractMonths, "month").format("YYYY-MM")
}
// Fetch GDCS options for FilterBar
const getGdcsData = async (territory?: string) => {
  try {
    scenarioDetails.value.concept = currentScenario.value.CNCPT_NM
    scenarioDetails.value.scenario_id = currentScenario.value.id
    const territoryName = territory ?? (currentScenario.value.territory_name ?? currentScenario.value.TERRITORY_NM)
    const response = await axios.post('scenario/getAllGDCSdata/', {
      concept: sessionStorage.getItem('concept') || "hb",
      scenario_id: sessionStorage.getItem('scenario_id'),
      loc_cd: JSON.parse(sessionStorage.getItem('loc_codes') || '[]'),
      territory_name: territoryName || "AE",
    })
    const gdcs = response.data.gdcs_data || []
    monthRangesByLocCd.value = response.data.month_ranges_by_loc_cd || []
    const firstStoreCode = response.data.gdcs_data?.[0]?.loc_cd
    const storeMonthRange = response.data.month_ranges_by_loc_cd?.find(
      item => item.loc_cd === firstStoreCode
    )
    startMonth.value = formatMonth(storeMonthRange?.start_month)
    endMonth.value = formatMonth(storeMonthRange?.end_month,1)
    scenarioDetails.value.gdcsData = response.data.gdcs_data || []
    scenarioDetails.value.monthRange = {
      start: startMonth.value,
      end: endMonth.value
    }
    if (gdcs.length > 0) {
      const firstStoreId = gdcs[0].loc_cd
      filters.value.from_month = startMonth.value
      filters.value.to_month = endMonth.value
    }
  } catch (err) {
    console.error('Error fetching GDCS Data:', err)
  }
}

// Fetch table data based on filters + pagination
const fetchTableData = async () => {
  isLoading.value = true
  try {
    const filterParams: { [key: string]: string[] } = {}
    for (const key in filters.value) {
      if (filters.value[key]?.length) {
        filterParams[`${key}[]`] = filters.value[key]
      }
    }
    const payload = {
      ref_period: {
        start: startMonth.value,
        end: endMonth.value
      },
      concept: (currentScenario.value.concept_name ?? currentScenario.value.CNCPT_NM) || "hb",
      territory: selectedTerritory.value || currentScenario.value.territory_name || currentScenario.value.TERRITORY_NM,
      scenario_id: currentScenario.value.id || currentScenario.value.scenario_id,
      filter_params: filterParams,
      page: currentPage.value,
      page_size: pageSize.value,
    }
    
    const response = await axios.post('scenario/health-metrics/', payload)
    const apiRows = response.data.results || []
    // const apiRows = [...response.data.results, ...response.data.summary_totals];
    tableData.value = apiRows.map(mapApiRowToTableRow)
    summaryTotals.value = response.data.summary_totals.map(mapApiRowToTableRow)
    currentPage.value = response.data.current_page
    pageSize.value = response.data.page_size
    totalRows.value = response.data.count
    totalPages.value = response.data.total_pages
    nextPageNum.value = response.data.next
    previousPageNum.value = response.data.previous
  } catch (err) {
    console.error('Error fetching table data:', err)
  } finally {
    isLoading.value = false
  }
}


// Handle filter changes from FilterBar
const onFiltersChanged = async (newFilters: any) => {
  filters.value = newFilters
  currentPage.value = 1
  await fetchTableData()
}

const clearTableFilters = () => {
  filters.value = {}
  currentPage.value = 1
  fetchTableData()
}

// Handle table pagination change
const onPageChanged = async (page: number) => {
  currentPage.value = page
  await fetchTableData()
}

// Initial load
onMounted(async () => {
  await getGdcsData()
  await fetchTableData()
})

const onTerritoryChange = async (newTerritory) => {
  selectedTerritory.value = newTerritory
  await getGdcsData(newTerritory)   // 🔥 Re-fetch GDCS data from backend
}
</script>

<template>
  <div class="flex flex-col min-h-screen overflow-auto">
    <!-- Loading State -->
    <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-2 flex items-center justify-center">
              <div class="tw-loader"></div> 
              <div class="mr-3">Loading...</div>
            </div>
    </div>
    <!-- Filter Bar -->
    <div class="flex-shrink-0 bg-secondary">
  <div class="max-w-7xl mx-auto px-4 py-2">
    <SpaceHealthFilterBar
      :table-data="tableData"
      :gdcs-data="scenarioDetails.gdcsData"
      :month-range="scenarioDetails.monthRange"
      @apply-filters="onFiltersChanged"
      @clear-filters="clearTableFilters"
      @territory-changed="onTerritoryChange"
    />
  </div>
</div>


    <!-- Table -->
    <div class="flex-1 overflow-auto p-4 ">
      <div class="p-4">
        <SpaceHealthMetricsTable
          :data="tableData"
          :summary-data="summaryTotals"
          :loading="isLoading"
          :current-page="currentPage"
          :page-size="pageSize"
          :total-rows="totalRows"
          :total-pages="totalPages"
          :next-page-num="nextPageNum"
          :previous-page-num="previousPageNum"
          @page-changed="onPageChanged"
        />


      </div>
    </div>
  </div>
</template>
