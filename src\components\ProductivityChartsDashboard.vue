<script setup>
import { ref, onMounted, nextTick, watch, computed } from 'vue'
import axios from 'axios'
import { useRoute, useRouter } from 'vue-router'
import * as echarts from 'echarts'
import DynamicFilter from './common/DynamicFilter.vue'
import { useStepsStore } from '../stores/NavigationStore.js'
import { updateScenarioStatus } from '../services/api.js'
import AlertPage from "./common/AlertPage.vue"

// Reactive data
const combinedChart = ref(null)
const router = useRouter()

// Methods
const getRange = (arr) => {
  if (!arr || arr.length === 0) return 'N/A'
  const min = Math.min(...arr).toFixed(2)
  const max = Math.max(...arr).toFixed(2)
  return `${min} - ${max}`
}

const initCombinedChart = () => {
  if (!combinedChart.value) return

  const chart = echarts.init(combinedChart.value)

  // Prepare data
  const actualData = saturationData.value.x_raw?.map((x, i) => [x, saturationData.value.y_actual[i]]) || []


  // Sort prediction data by x values for smooth curve
  // Sort + dedupe prediction data
  const sortedPredData = _sortDedupeXY(
    saturationData.value.x_pred_lm,
    saturationData.value.y_pred
  )

  // --- Polynomial Fit Curve ---
  // Only if degree and data are present
  let polyFitData = []
  const degree = Number(saturationData.value.PolynomialDegree) || 3
  const x_raw = (saturationData.value.x_raw || []).slice()
  const y_actual = (saturationData.value.y_actual || []).slice()
  if (x_raw.length > 0 && y_actual.length > 0) {
    // Fit polynomial using least squares (JS version)
    // 1. Sort x/y by x
    const xy = x_raw.map((x, i) => [x, y_actual[i]]).sort((a, b) => a[0] - b[0])
    const xs = xy.map(([x]) => x)
    const ys = xy.map(([, y]) => y)
    // 2. Vandermonde matrix
    function vandermonde(x, deg) {
      return x.map(xi => Array.from({ length: deg + 1 }, (_, d) => Math.pow(xi, d)))
    }
    // 3. Least squares fit (pseudo-inverse)
    function polyfit(x, y, deg) {
      const X = vandermonde(x, deg)
      // X^T X
      const XT = X[0].map((_, c) => X.map(r => r[c]))
      const XTX = XT.map(row => XT.map((_, j) => row.reduce((sum, v, i) => sum + v * X[i][j], 0)))
      // X^T y
      const XTy = XT.map(row => row.reduce((sum, v, i) => sum + v * y[i], 0))
      // Solve XTX * coef = XTy (naive Gaussian elimination)
      function gauss(A, b) {
        const n = A.length
        for (let i = 0; i < n; i++) {
          // Partial pivot
          let maxRow = i
          for (let k = i + 1; k < n; k++) if (Math.abs(A[k][i]) > Math.abs(A[maxRow][i])) maxRow = k
            ;[A[i], A[maxRow]] = [A[maxRow], A[i]]
            ;[b[i], b[maxRow]] = [b[maxRow], b[i]]
          // Eliminate
          for (let k = i + 1; k < n; k++) {
            const c = A[k][i] / A[i][i]
            for (let j = i; j < n; j++) A[k][j] -= c * A[i][j]
            b[k] -= c * b[i]
          }
        }
        // Back-substitute
        const x = Array(n).fill(0)
        for (let i = n - 1; i >= 0; i--) {
          x[i] = (b[i] - A[i].slice(i + 1).reduce((sum, v, j) => sum + v * x[i + 1 + j], 0)) / A[i][i]
        }
        return x
      }
      return gauss(XTX, XTy)
    }
    // 4. Generate fit curve
    const coef = polyfit(xs, ys, degree)
    // Generate 200 points in x range
    const minX = Math.min(...xs)
    const maxX = Math.max(...xs)
    const x_range = Array.from({ length: 200 }, (_, i) => minX + (maxX - minX) * i / 199)
    polyFitData = x_range.map(x => [x, coef.reduce((sum, c, d) => sum + c * Math.pow(x, d), 0)])
  }

  // Saturation point
  const saturationPoint = [saturationData.value.sat_lm, saturationData.value.perf_at_sat]

  // Calculate min/max for x and y
  const allX = [...(saturationData.value.x_raw || []), ...(saturationData.value.x_pred_lm || []), saturationData.value.sat_lm].filter(v => v !== undefined && v !== null)
  const allY = [...(saturationData.value.y_actual || []), ...(saturationData.value.y_pred || []), saturationData.value.perf_at_sat].filter(v => v !== undefined && v !== null)
  const minX = Math.min(...allX)
  const maxX = Math.max(...allX)
  const minY = Math.min(...allY)
  const maxY = Math.max(...allY)
  // Add padding
  const xPad = (maxX - minX) * 0.15 || 1
  const yPad = (maxY - minY) * 0.15 || 1
  const paddedMinX = minX - xPad
  const paddedMaxX = maxX + xPad
  const paddedMinY = minY - yPad
  const paddedMaxY = maxY + yPad

  // Create vertical line data for saturation
  const saturationLine = [
    [saturationData.value.sat_lm, paddedMinY],
    [saturationData.value.sat_lm, paddedMaxY]
  ]

  const option = {
    backgroundColor: 'transparent',
    // title: {
    //   text: `Performance Prediction for Subclass: ${saturationData.value.subclass_name}`,
    //   left: 'center',
    //   textStyle: {
    //     fontSize: 16,
    //     fontWeight: 'bold'
    //   }
    // },
    tooltip: {
      trigger: 'item',
      formatter: function (params) {
        if (params.seriesName === 'Saturation Performance') {
          return `Saturation Performance<br/>LM: ${params.value[0].toFixed(3)}<br/>Performance: ${params.value[1].toFixed(2)}`
        }
        return `${params.seriesName}<br/>Linear Meter (LM): ${params.value[0].toFixed(2)}<br/>Predicted Performance: ${params.value[1].toFixed(2)}`
      }
    },
    legend: {
      data: ['Predicted Performance', 'Original Data', 'Saturation Point', 'Saturation Performance'],
      bottom: 0,
      textStyle: {
        fontSize: 12
      }
    },
    grid: {
      left: '10%',
      right: '10%',
      bottom: '15%',
      top: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'value',
      name: 'Linear Meter (LM)',
      nameLocation: 'middle',
      nameGap: 35,
      min: paddedMinX.toFixed(2),
      max: paddedMaxX.toFixed(2),
      axisLine: {
        lineStyle: { color: '#666' },
        show: true
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#e5e7eb'
        }
      }
    },
    yAxis: {
      type: 'value',
      name: 'Predicted Performance',
      nameLocation: 'middle',
      nameGap: 60,
      min: paddedMinY.toFixed(2),
      max: paddedMaxY.toFixed(2),
      axisLine: {
        lineStyle: { color: '#666' },
        show: true
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: '#e5e7eb'
        }
      }
    },
    series: [
      // {
      //   name: 'Predicted Performance',
      //   type: 'line',
      //   data: sortedPredData,
      //   smooth: true,
      //   lineStyle: {
      //     color: '#2563eb',
      //     width: 3
      //   },
      //   itemStyle: {
      //     color: '#2563eb'
      //   },
      //   symbolSize: 0,
      //   showSymbol: false
      // },
      {
        name: 'Polynomial Fit',
        type: 'line',
        data: polyFitData,
        smooth: true,
        lineStyle: {
          color: '#1d4ed8',
          width: 2,
          // type: 'dotted'
        },
        itemStyle: {
          color: '#1d4ed8'
        },
        symbolSize: 0,
        showSymbol: false
      },
      {
        name: 'Original Data',
        type: 'scatter',
        data: actualData,
        symbolSize: 8,
        itemStyle: {
          color: '#dc2626'
        },
        emphasis: {
          itemStyle: {
            borderColor: '#991b1b',
            borderWidth: 2
          }
        }
      },
      {
        name: 'Saturation Point',
        type: 'line',
        data: saturationLine,
        lineStyle: {
          color: '#abc4ff',
          width: 2,
          type: 'dashed'
        },
        symbol: 'none',
        silent: true
      },
      {
        name: 'Saturation Performance',
        type: 'scatter',
        data: [saturationPoint],
        symbolSize: 12,
        itemStyle: {
          color: '#7c3aed',
          borderColor: '#5b21b6',
          borderWidth: 2
        },
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowColor: '#7c3aed'
          }
        }
      }
    ]
  }

  chart.setOption(option)
  window.addEventListener('resize', () => chart.resize())
}
const _sortDedupeXY = (xArr, yArr) => {
  if (!xArr || !yArr) return []

  // Pair X and Y into tuples
  const pairs = xArr.map((x, i) => [x, yArr[i]])

  // Sort by X
  pairs.sort((a, b) => a[0] - b[0])

  // Deduplicate by averaging Y for repeated X
  const deduped = []
  let currentX = null
  let values = []

  for (const [x, y] of pairs) {
    if (currentX === null) {
      currentX = x
      values = [y]
    } else if (x === currentX) {
      values.push(y)
    } else {
      deduped.push([currentX, values.reduce((a, b) => a + b, 0) / values.length])
      currentX = x
      values = [y]
    }
  }
  if (currentX !== null) {
    deduped.push([currentX, values.reduce((a, b) => a + b, 0) / values.length])
  }

  return deduped
}


// Lifecycle
onMounted(async () => {
  await getClusterSubclassData()
  await getSaturationData()
})
const stepsStore = useStepsStore()
const storedData = stepsStore.getScenarioData;
let saturationData = ref([]);
let clusterSubclassData = ref([]);
let selectedClusters = ref(null)
let selectedSubClasses = ref(null)
const isLoading = ref(false)
const getSaturationData = async () => {
  isLoading.value = true
  try {
    const response = await axios.post('scenario/getSaturationCurve/', {
      concept: sessionStorage.getItem('concept'),
      scenario_id: sessionStorage.getItem('scenario_id'),
      sub_class: selectedSubClasses.value,
      cluster: selectedClusters.value,
    })
    saturationData.value = response.data.data;
    initCombinedChart()
  } catch (err) {
    console.error('Error fetching GDCS Data:', err)
  } finally {
    isLoading.value = false
  }
}
const getClusterSubclassData = async () => {
  try {
    const response = await axios.post('scenario/getClusterSubclassData/', {
      concept: sessionStorage.getItem('concept'),
      scenario_id: sessionStorage.getItem('scenario_id'),
    })
    clusterSubclassData.value = response.data.data;
    if (clusterSubclassData.value.length > 0) {
      selectedClusters.value = clusterSubclassData.value[0].cluster
      selectedSubClasses.value = clusterSubclassData.value[0].subclass
    }
  } catch (err) {
    console.error('Error fetching getClusterSubclassDta Data:', err)
  }
}
const completeOptimization = async () => {
  try {
    await axios.post('scenario/completeOptimization/', {
      scenario_id: sessionStorage.getItem('scenario_id'),
    })
    router.push({ name: 'HomePage' })
  } catch (err) {
    console.error('Error completing optimization:', err)
  }
}
function getUniqueClusters() {
  const clusters = [...new Set(clusterSubclassData.value.map(item => item.cluster))];
  return clusters.map(c => ({ label: `Cluster ${c}`, value: c }));
}

function getUniqueClasses() {
  const subclasses = clusterSubclassData.value
    .filter(item => item.cluster === selectedClusters.value)
    .map(item => item.subclass);
  return [...new Set(subclasses)].map(s => ({ label: s, value: s }));
}
const saveOptimiserProgress = async (action) => {
  try {
    await updateScenarioStatus({
      scenario_id: sessionStorage.getItem('scenario_id'),
      current_page: stepsStore.currentStep,
      progress_page: stepsStore.currentStep
    })
    if (action === 'next') {
      stepsStore.goToNextStep()
    } else if (action === 'prev') {
      stepsStore.goToPreviousStep()
    }
  } catch (err) {
    console.error('Error updating scenario status:', err)
  }
}

const showAlert = ref(false)
const alertMessage = ref(`Your setup is now complete.
The evaluation will run in the background.
Please return after the evaluation period to view the results.`)
const headerMessage = ref("All Steps Completed")
const handleOk = () => {
  completeOptimization()
  saveOptimiserProgress('next')
  showAlert.value = false
}


const handleCancel = () => {
  showAlert.value = false
}
</script>

<template>
  <div class="px-4">
    <div class="rounded-lg px-2">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-2" style="align-items: flex-end">
        <div>
          <label class="ml-2 text-sm font-medium text-gray-700">Cluster</label>
          <DynamicFilter v-model="selectedClusters" :multiselect="false" :close-on-select="!multiselect" label="Store"
            placeholder="Stores" :options="getUniqueClusters()" variant="secondary" size="sm"
            @change="getUniqueClasses()" />
        </div>
        <div>
          <label class="ml-2 text-sm font-medium text-gray-700">Sub Class</label>
          <DynamicFilter v-model="selectedSubClasses" :multiselect="false" :close-on-select="!multiselect"
            label="Classes" placeholder="Sub Class" :options="getUniqueClasses()" variant="secondary" size="sm" />
        </div>
        <div class="flex items-end">
          <button @click="getSaturationData()"
            class="px-4 w-1/2 h-10 justify-center  text-sm font-medium text-tx-primary bg-tertiary border border-transparent rounded-md hover:bg-tertiary focus:outline-none focus:ring-2 focus:ring-blue-500">
            Apply Filters
          </button>
        </div>
      </div>
    </div>
    <div class="flex flex-col bg-white rounded-lg shadow-lg px-4 py-1">
      <div class="flex gap-4 mb-1">
        <div class="flex-1 bg-blue-50 p-2 rounded-lg flex items-center justify-between">
          <p class="text-xs text-gray-600">Subclass:</p>
          <p class="text-sm font-semibold text-blue-800">{{ saturationData.subclass_name }}</p>
        </div>
        <div class="flex-1 bg-green-50 p-2 rounded-lg flex items-center justify-between">
          <p class="text-xs text-gray-600">Cluster:</p>
          <p class="text-sm font-semibold text-green-800">{{ saturationData.cluster }}</p>
        </div>
        <div class="flex-1 bg-purple-50 p-2 rounded-lg flex items-center justify-between">
          <p class="text-xs text-gray-600">Saturation LM:</p>
          <p class="text-sm font-semibold text-purple-800">{{ saturationData.sat_lm }}</p>
        </div>
        <div class="flex-1 bg-orange-50 p-2 rounded-lg flex items-center justify-between">
          <p class="text-xs text-gray-600">Performance at Saturation:</p>
          <p class="text-sm font-semibold text-orange-800">{{ saturationData.perf_at_sat }}</p>
        </div>
      </div>

      <div class="bg-gray-50 px-4 rounded-lg">
        <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50">
            <div class="bg-white rounded-lg p-2 flex items-center justify-center">
              <div class="tw-loader"></div> 
              <div class="mr-3">Loading chart...</div>
            </div>
        </div>
        <div ref="combinedChart" style="width: 100%; height: 400px;"></div>
      </div>
    </div>
    <!-- <div class="flex justify-between m-4">
      <button @click="saveOptimiserProgress('prev')"
        class="border border-gray-300 text-tx-primary px-4 font-semibold py-2  mb-8 rounded hover:bg-gray-100 ">
        ← Previous
      </button>
      <button @click="showAlert = true"
        class="bg-secondary hover:bg-tertiary text-tx-primary font-semibold mr-8 mb-8 cursor-pointer hover:bg-tertiary px-6 py-2 rounded mr-2">
        Complete →
      </button>
    </div> -->
  </div>
  <AlertPage :message="alertMessage" :header="headerMessage" :visible="showAlert" @ok="handleOk"
    @cancel="handleCancel" />
</template>

<style scoped>
/* Additional custom styles if needed */
.chart-container {
  min-height: 400px;
}
</style>