<script setup lang="ts">
import { reactive, computed, onMounted, ref, watch, toRaw } from "vue";
import {RotateCcw } from "lucide-vue-next";
import { useToast } from 'vue-toastification'

const toast = useToast()

const props = defineProps({
  isDisabled: {
    type: Boolean,
    default: false
  }
})
const isDisabled = computed(() => props.isDisabled)
const DEFAULT_WEIGHTS = {
  cover: 0.15,
  gmv_margin_pct: 0.25,
  gmv_per_lm: 0.25,
  sub_cpen: 0.1,
  d_units: 0.15,
  asp: 0.1,
};

const weights = reactive({ ...DEFAULT_WEIGHTS });
const originalWeights = ref({})
const isModified = ref(false)
let initialized = false
const isReset = ref(true);

onMounted(() => {
  const stored = sessionStorage.getItem('weights');
  if (stored) {
    try {
      const parsed = JSON.parse(stored);
      Object.keys(DEFAULT_WEIGHTS).forEach(key => {
        if (parsed[key] !== undefined) {
          weights[key] = parsed[key];
        }
      });
    } catch {
    }
    originalWeights.value = JSON.parse(JSON.stringify(weights))
    isModified.value = true

  }
  checkIfModified();
  initialized = true
});

function checkIfModified() {
  const same = JSON.stringify(toRaw(weights)) === JSON.stringify(DEFAULT_WEIGHTS);
  isReset.value = same; // if not same, show Reset
}

watch(
  () => weights,
  (newVal) => {
    if (!initialized) {
      initialized = true; // Skip the first run
      return;
    }
    checkIfModified();
    // const same = JSON.stringify(toRaw(newVal)) === JSON.stringify(toRaw(originalWeights.value))
    // isModified.value = !same
    // isReset.value = same;
  },
  { deep: true }
)

const totalWeight = computed(() =>
  Object.values(weights).reduce((sum, v) => sum + Number(v), 0)
);

function formatPercent(value) {
  return (value * 100).toFixed(0);
}

const resetWeights = async () => {
  try {
    // Update reactive weights
    Object.keys(weights).forEach(key => {
      weights[key] = DEFAULT_WEIGHTS[key]
    })

    isModified.value = true
    initialized = false
    isReset.value = true

    toast.success('Weights reset to default successfully!')
  } catch (error) {
    toast.error('Failed to reset weights')
  }
}


function parsePercent(event, key) {
  let val = parseFloat(event.target.value) || 0;
  weights[key] = val / 100;
}
</script>

<template>
  <div class="flex items-center justify-center rounded-lg bg-gray-50">
    <div class="bg-white shadow-lg rounded-lg p-6">
      <div class="flex justify-between items-center mb-4 p-2 rounded-lg">
        <h2 class="text-lg font-semibold">Configure Weights</h2>
      </div>

      <div class="space-y-4">
        <!-- Units per Invoice -->
        <div class="flex items-center">
          <label class="w-48 text-gray-700">Units per Invoice</label>
          <div class="flex items-center">
            <input type="number" class="border rounded-lg p-2 w-24 text-right" :value="formatPercent(weights.d_units)"
              @input="parsePercent($event, 'd_units')" />
            <span class="ml-1">%</span>
          </div>
        </div>

        <!-- Customer Penetration -->
        <div class="flex items-center">
          <label class="w-48 text-gray-700">Customer Penetration</label>
          <div class="flex items-center">
            <input type="number" class="border rounded-lg p-2 w-24 text-right" :value="formatPercent(weights.sub_cpen)"
              @input="parsePercent($event, 'sub_cpen')" />
            <span class="ml-1">%</span>
          </div>
        </div>

        <!-- Store Cover -->
        <div class="flex items-center">
          <label class="w-48 text-gray-700">Cover</label>
          <div class="flex items-center">
            <input type="number" class="border rounded-lg p-2 w-24 text-right" :value="formatPercent(weights.cover)"
              @input="parsePercent($event, 'cover')" />
            <span class="ml-1">%</span>
          </div>
        </div>

        <!-- Margin % -->
        <div class="flex items-center">
          <label class="w-48 text-gray-700">Margin</label>
          <div class="flex items-center">
            <input type="number" class="border rounded-lg p-2 w-24 text-right"
              :value="formatPercent(weights.gmv_margin_pct)" @input="parsePercent($event, 'gmv_margin_pct')" />
            <span class="ml-1">%</span>
          </div>
        </div>

        <!-- Productivity (GMV per LM) -->
        <div class="flex items-center">
          <label class="w-48 text-gray-700">Productivity</label>
          <div class="flex items-center">
            <input type="number" class="border rounded-lg p-2 w-24 text-right"
              :value="formatPercent(weights.gmv_per_lm)" @input="parsePercent($event, 'gmv_per_lm')" />
            <span class="ml-1">%</span>
          </div>
        </div>

        <!-- Average Selling Price -->
        <div class="flex items-center">
          <label class="w-48 text-gray-700">Average Selling Price</label>
          <div class="flex items-center">
            <input type="number" class="border rounded-lg p-2 w-24 text-right" :value="formatPercent(weights.asp)"
              @input="parsePercent($event, 'asp')" />
            <span class="ml-1">%</span>
          </div>
        </div>
      </div>

      <!-- Total Weight -->
      <div class="mt-6 text-right font-semibold" :class="Number(totalWeight.toFixed(2)) === 1 ? 'text-green-600' : 'text-red-600'">
        Total Weight: {{ (totalWeight * 100).toFixed(0) }}%
      </div>
      <div class="flex justify-end gap-3 mt-6">
        <div class="relative inline-flex items-center group">
  <!-- Reset Button -->
  <button
    class="px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-100 flex items-center gap-2
           disabled:bg-gray-200 disabled:text-gray-400 disabled:cursor-not-allowed"
    @click="resetWeights"
    :disabled="isReset"
  >
    <RotateCcw class="w-4 h-5" />
  </button>

  <!-- Tooltip -->
  <div
    class="absolute bottom-full left-1/2 -translate-x-1/2 mb-1 w-24 rounded-md bg-gray-800 text-white text-[11px] leading-snug p-2.5 shadow-lg
           opacity-0 pointer-events-none transition-opacity group-hover:opacity-100 group-hover:pointer-events-auto whitespace-normal break-words text-center"
  >
    Set to Default
  </div>
</div>

      
        <button class="px-4 py-2 border border-gray-300 rounded-lg  hover:bg-gray-100" @click="$emit('close')">
          Close
        </button>
        <button class="px-4 py-2 rounded-xl bg-tertiary text-tx-primary hover:bg-tertiary disabled:bg-gray-400 disabled:hover:bg-gray-400 
         disabled:cursor-not-allowed" @click="$emit('apply', { ...weights })" :disabled="Number(totalWeight.toFixed(2)) !== 1 || isDisabled">
          Recalculate
        </button>
      </div>
    </div>
  </div>
</template>
