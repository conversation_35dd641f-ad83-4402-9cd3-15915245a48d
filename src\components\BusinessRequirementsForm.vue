<template>
  <div class="flex flex-col flex-1 px-4 sm:px-6 lg:px-8 pt-4">
    <div class=" rounded shadow">
      <div>
        <!-- <div class="bg-white rounded-2xl shadow-sm border border-gray-200"> -->
        <div v-if="isLoading" class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50 p-2">
          <div class="bg-white rounded-lg p-2 flex items-center justify-center">
            <div class="tw-loader"></div> 
            <div class="mr-3">Loading...</div>
          </div>
        </div>
        <div>
          <div class="px-4 py-6 sm:px-6 sm:py-8 lg:px-8">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div class="space-y-3">
                <label class="flex items-center text-sm font-medium mb-2">
                  <StickyNote class="w-4 h-4 mr-2 text-tertiary" />Scenario Name<span
                    class="text-red-500 ml-1">*</span>
                </label>
                <input type="text" v-model="formData.scenarioName" placeholder="Enter Scenario Name"
                  class="w-full h-10 px-4 text-sm font-medium rounded-lg border border-gray-300 bg-white focus:outline-none focus:ring-2 disabled:text-gray-400 focus:ring-primary" :disabled="isDisabled" />
              </div>


              <div class="space-y-5">
                <label class="flex items-center text-sm font-semibold">
                  <Bolt class="w-4 h-4 mr-2 text-tertiary" />
                  Territory <span class="text-red-500 ml-1">*</span>
                </label>
                <DynamicFilter v-model="formData.territory" :multiselect="false" label="Territories" :disabled="isDisabled"
                  placeholder="Select Territories" :options="territories" value-key="value" label-key="label" 
                  :searchable="false" :close-on-select="!multiselect" variant="outline" size="sm" @change="handleTerritoryChange()" />

              </div>
              <!-- Store Configuration -->
              <div class="space-y-3">
                <label class="flex items-center text-sm font-semibold">
                  <Bolt class="w-4 h-4 mr-2 text-tertiary" />
                  Store Configuration <span class="text-red-500 ml-1">*</span>
                </label>
                <DynamicFilter v-model="formData.configurationStore" :multiselect="false" label="Store Configuration"
                  placeholder="Select Store Configuration" :disabled="isDisabled" :options="configurationStoreOptions" :searchable="false"
                  :close-on-select="!multiselect" variant="outline" size="sm"
                  @change="setStore(formData.configurationStore); handleTerritoryChange()" />

              </div>

              <div class="space-y-3">
                <label class="flex items-center text-sm font-semibold">
                  <SunSnow class="w-4 h-4 mr-2 text-tertiary" />
                  Season Type <span class="text-red-500 ml-1">*</span>
                </label>
                <DynamicFilter v-model="formData.seasonType" :multiselect="false" label="Season Type" :disabled="isDisabled"
                  placeholder="Select Season Type" :options="seasonTypeOptions" value-key="value" label-key="label"
                  :searchable="false" :close-on-select="!multiselect" variant="outline" size="sm"
                  @update:modelValue="onSeasonTypeSelect" />
              </div>


              <!-- Performance Metric -->
              <div class="space-y-3">
                <label class="flex items-center text-sm font-semibold">
                  <Target class="w-4 h-4 mr-2 text-tertiary" />
                  Performance Metric <span class="text-red-500 ml-1">*</span>
                </label>  
                <DynamicFilter v-model="formData.performanceMetric" :multiselect="false" label="Performance metric" :disabled="isDisabled"
                  placeholder="Select performance metric" :options="performanceOptions" value-key="value"
                  label-key="label" :searchable="false" :close-on-select="!multiselect" variant="outline" size="sm" @change="reRunOptimizer()"/>

              </div>

              <div class="space-y-3">
                <label class="flex items-center text-sm font-semibold">
                  <SunSnow class="w-4 h-4 mr-2 text-tertiary" />
                  Event <span class="text-red-500 ml-1">*</span>
                </label>
                <input type="text" v-model="formData.event" placeholder="Enter Event Name"
                  :readonly="formData.seasonType === 'in season'" :class="[
                    'w-full h-10 px-4 text-sm font-medium rounded-lg border focus:outline-none focus:ring-2',
                    formData.seasonType === 'in season'
                      ? 'bg-gray-50 text-gray-500 border-gray-200 cursor-not-allowed'
                      : 'bg-white text-tx-tertiary border-gray-300 focus:ring-primary'
                  ]" />
              </div>
                    <div v-if="formData.configurationStore === 'Selected Stores'" class="mt-6">
        <div class="space-y-3">
          <label class="flex items-center text-sm font-semibold">
            <Filter class="w-4 h-4 mr-2 text-tertiary" />
            Store Selection <span class="text-red-500 ml-1">*</span>
          </label>
          <DynamicFilter :disabled="isDisabled"
          v-model="formData.storeSelection"
          :multiselect="true"
          label="stores"
          placeholder="Select stores"
          :options="storeOptions"
          value-key="value"
          label-key="label"
          :searchable="true"
          variant="outline"
          size="sm" @change="reRunOptimizer()"
        />
          
        </div>
            </div>

      <!-- Store Selection -->

      </div>

            <!-- Evaluation Period -->
            <div class="mt-8 text-sm">
              <h3 class="text-md font-semibold mb-4 flex items-center">
                <div class="w-8 h-8 rounded-lg flex items-center justify-center">
                  <Calendar class="w-4 h-4 text-tertiary" />
                </div>
                Evaluation Period <span class="text-red-500 ml-1">*</span>
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
                <div>
                  <label class="block text-sm font-medium mb-2">Start Date</label>
                  <DatePicker v-model="formData.evaluationPeriod.startDate" showIcon dateFormat="dd-mm-yy"
                    placeholder="Select Start Date" @date-select="validateEvaluationPeriod"
                    class="text-sm w-full text-xs h-8 px-2" :class="{ 'p-invalid': !isEvaluationPeriodValid }"
                    panelClass="date-panel-custom" :disabled="isDisabled" />
                </div>

                <div>
                  <label class="block text-sm font-medium mb-2">End Date</label>
                  <DatePicker v-model="formData.evaluationPeriod.endDate" :showIcon="true" dateFormat="dd-mm-yy"
                    placeholder="Select End Date" @date-select="validateEvaluationPeriod"
                    class="w-full" :class="{'p-invalid': !isEvaluationPeriodValid}"
                    panelClass="date-panel-custom" :disabled="isDisabled" />
                </div>
              </div>
            </div>

            <!-- Reference Period -->
            <div class="mt-8 text-sm">
              <h3 class="text-md font-semibold mb-4 flex items-center">
                <div class="w-8 h-8 rounded-lg flex items-center justify-center">
                  <Calendar class="w-4 h-4 text-tertiary" />
                </div>
                Reference Period <span class="text-red-500 ml-1">*</span>
                <div v-if="storeAvailability.length > 0" class="relative">
                <span
                  ref="tooltipTrigger"
                  class="ml-1 text-black-400 hover:text-gray-700 font-bold border border-gray-300 rounded-full w-4 h-4 flex items-center justify-center text-[0.55rem] bg-gray-200 cursor-pointer"
                  aria-label="Information"
                  @mouseenter="handleTooltipShow"
                  @mouseleave="handleTooltipHide"
                  @focus="handleTooltipShow"
                  @blur="handleTooltipHide"
                  @click="handleTooltipClick"
                  tabindex="0"
                >
                  i
                </span>
                  <div
                    v-if="showTooltip"
                    ref="tooltipContent"
                    :class="[
                      'absolute w-48 max-w-sm rounded-lg bg-gray-700 text-white text-[9px] leading-snug px-3 py-2 shadow-lg z-50 transition-opacity',
                      getTooltipPositionClasses()
                    ]">
                    <div class="font-semibold mb-2 text-center z-1000">Months data available for stores</div>
                    <div class="h-auto z-50">
                      <table class="w-full text-[8px]">
                        <thead>
                          <tr class="border-b border-gray-500">
                            <th class="text-left py-1 px-1">Store ID</th>
                            <th class="text-left py-1 px-1">From</th>
                            <th class="text-left py-1 px-1">To</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="store in storeAvailability" :key="store.store" class="border-b border-gray-600">
                            <td class="py-1 px-1">{{ store.store }}</td>
                            <td class="py-1 px-1">{{ formatAvailabilityMonth(store.min_month) }}</td>
                            <td class="py-1 px-1">{{ formatAvailabilityMonth(store.max_month) }}</td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                </div>
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label class="block text-sm font-medium mb-2">Start Month</label>
                  <DatePicker 
                    v-model="formData.referencePeriod.startDate"
                    :showIcon="true"
                    view="month"
                    dateFormat="M yy"
                    :minDate="new Date(startMonth)"
                    :maxDate="new Date(endMonth)"
                    placeholder="Select Start Month"
                    @date-select="validateReferencePeriod"
                    class="w-full datepicker-custom"
                    :class="{'p-invalid': !isReferencePeriodValid}"
                    panelClass="date-panel-custom" :disabled="isDisabled"
                  />
                </div>
                <div>
                  <label class="block text-sm font-medium mb-2">End Month</label>
                  <DatePicker 
                    v-model="formData.referencePeriod.endDate"
                    :showIcon="true"
                    view="month"
                    dateFormat="M yy"
                    :minDate="new Date(startMonth)"
                    :maxDate="new Date(endMonth)"
                    placeholder="Select End Month"
                    @date-select="validateReferencePeriod"
                    class="w-full datepicker-custom"
                    :class="{'p-invalid': !isReferencePeriodValid}"
                    panelClass="date-panel-custom" :disabled="isDisabled"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- File Upload Section -->
      <div class="p-10">
        <div class="flex items-center mb-4">
          <div class="w-8 h-8 rounded-xl flex items-center justify-center mr-4">
            <Upload class="w-4 h-4 text-tertiary" />
          </div>
          <div>
            <h2 class="text-md font-bold">Upload Files</h2>
          </div>
        </div>

    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 px-2">
      <FileUploadArea fileKey="sqft" label="Square Feet" :icon="BarChart3" @upload-result="handleUploadResult" :scenarioPhase="isDisabled" />
      <FileUploadArea fileKey="mdq" label="MDQ" :icon="TrendingUp" @upload-result="handleUploadResult" :scenarioPhase="isDisabled" />
      <FileUploadArea fileKey="cover" label="Target Covers" :icon="FileText" @upload-result="handleUploadResult" :scenarioPhase="isDisabled" />
      <FileUploadArea fileKey="exclusion" label="Exclusions" :icon="SquaresExclude" @upload-result="handleUploadResult" :scenarioPhase="isDisabled" />
    </div>

        <!-- <div class="flex justify-end mt-8">
      <button
        @click="validateAndContinue"
        :disabled="isLoading"
        :class="{
          'bg-secondary hover:bg-green-700 text-tx-primary cursor-pointer': !isLoading,
          'bg-gray-300 text-gray-500 cursor-not-allowed': isLoading
        }"
        class="px-8 py-3 rounded-lg font-semibold text-sm transition-colors duration-200 flex items-center"
      >
        <div v-if="isLoading" class="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
        Next →
      </button>
    </div> -->
      </div>

      <PopupModal :show="showPopup" :message="popupMessage" @close="showPopup = false" />
    </div>
  </div>
</template>




<script setup>

import { onMounted, onUnmounted, ref, reactive, provide, toRaw, watch, computed, defineProps, defineExpose, nextTick } from 'vue'
import Multiselect from 'vue-multiselect'
import DynamicFilter from './common/DynamicFilter.vue'
import DatePicker from 'primevue/datepicker'
import FileUploadArea from './FileUploadArea.vue'
import { StickyNote, Calendar, Filter, ChevronDown, Space, Target, Check, X, Upload, BarChart3, TrendingUp, FileText, SquaresExclude, Bolt, SunSnow } from 'lucide-vue-next'
import { fetchStores, saveScenarioAPI, updateScenarioStatus, fetchRefPeriod, setOptimizerRunStatus,fetchStoreDataAvailability } from '../services/api'
import { useStepsStore } from '../stores/NavigationStore'
import { useScenarioStore } from '../stores/ScenarioStore'
import PopupModal from './PopUpModal.vue'
import axios from 'axios'
import { useRoute } from 'vue-router'
const route = useRoute();
const stepsStore = useStepsStore()
const scenarioStore = useScenarioStore()
import { inject } from 'vue';
const baseImgUrl = inject('baseImageUrl');

// Add loading state for continue button
const isLoading = ref(false)
const startMonth = ref('2024-08')
const endMonth = ref('2025-09')
const showPopup = ref(false)
const popupMessage = ref('')
const storeAvailability = ref([]) // Initialize with empty array
const showTooltip = ref(false)
const tooltipPosition = ref('bottom') // 'bottom', 'top', 'right', 'left'
const tooltipTrigger = ref(null)
const tooltipContent = ref(null)

const props = defineProps({
  scenarioPhase: {
    type: String,
    default: ''
  }
})
const isDisabled = computed(() => props.scenarioPhase === 'completed')

const calculateTooltipPosition = () => {
  if (!tooltipTrigger.value || !tooltipContent.value) return

  const triggerRect = tooltipTrigger.value.getBoundingClientRect()
  const viewportWidth = window.innerWidth
  const viewportHeight = window.innerHeight

  // Estimate tooltip dimensions (will be refined after first render)
  const tooltipWidth = 200 // approximate width
  const tooltipHeight = 150 // approximate height

  const spaceBelow = viewportHeight - triggerRect.bottom - 140
  const spaceAbove = triggerRect.top + 140
  const spaceRight = viewportWidth - triggerRect.right - 140
  const spaceLeft = triggerRect.left + 140

  // Position priority: Bottom → Top → Right → Left
  if (spaceBelow >= tooltipHeight) {
    tooltipPosition.value = 'bottom'
  } else if (spaceAbove >= tooltipHeight) {
    tooltipPosition.value = 'top'
  } else if (spaceRight >= tooltipWidth) {
    tooltipPosition.value = 'right'
  } else if (spaceLeft >= tooltipWidth) {
    tooltipPosition.value = 'left'
  } else {
    // Fallback to position with most space
    const maxSpace = Math.max(spaceBelow, spaceAbove, spaceRight, spaceLeft)
    if (maxSpace === spaceBelow) tooltipPosition.value = 'bottom'
    else if (maxSpace === spaceAbove) tooltipPosition.value = 'top'
    else if (maxSpace === spaceRight) tooltipPosition.value = 'right'
    else tooltipPosition.value = 'left'
  }
}

const handleTooltipShow = () => {
  showTooltip.value = true
  // Use nextTick to ensure DOM is updated before calculating position
  nextTick(() => {
    calculateTooltipPosition()
  })
}

const handleTooltipHide = () => {
  showTooltip.value = false
}

const handleTooltipClick = () => {
  if (showTooltip.value) {
    handleTooltipHide()
  } else {
    handleTooltipShow()
  }
}

const handleScroll = () => {
  if (showTooltip.value) {
    calculateTooltipPosition()
  }
}

const getTooltipPositionClasses = () => {
  switch (tooltipPosition.value) {
    case 'top':
      return 'bottom-full mb-2 left-1/2 -translate-x-1/2'
    case 'bottom':
      return 'top-full mt-2 left-1/2 -translate-x-1/2'
    case 'right':
      return 'left-full ml-2 top-1/2 -translate-y-1/2'
    case 'left':
      return 'right-full mr-2 top-1/2 -translate-y-1/2'
    default:
      return 'top-full mt-2 left-1/2 -translate-x-1/2'
  }
}

const handleClickOutside = (event) => {
  if (showTooltip.value &&
      tooltipTrigger.value &&
      tooltipContent.value &&
      !tooltipTrigger.value.contains(event.target) &&
      !tooltipContent.value.contains(event.target)) {
    handleTooltipHide()
  }
}

const getStoreAvailability = async () => {
  storeAvailability.value = [] // Reset the store availability array before fetching
  try {
    const result = await fetchStoreDataAvailability(formData)

    if (result && Array.isArray(result)) {
      storeAvailability.value = result
    }
  } catch (error) {
    console.error("Error in getStoreAvailability:", error)
  }
}
const files = ref({
  sqft: { filename: null, fileId: null, fileObject: null, error: false },
  mdq: { filename: null, fileId: null, fileObject: null, error: false },
  cover: { filename: null, fileId: null, fileObject: null, error: false },
  exclusion: { filename: null, fileId: null, fileObject: null, error: false }
})

const dragActive = ref(null)
const loadingStores = ref(false)
const isEvaluationPeriodValid = ref(true)
const isReferencePeriodValid = ref(true)

// Validate mandatory fields and show popup if missing


// Expose beforeLeave for parent to await before leaving/unmounting
async function beforeLeave() {
  return await validateAndContinue();
}
const validateAndContinue = async () => {
  let missingFields = [];
  if (!formData.scenarioName) missingFields.push('Scenario Name');
  if (!formData.territory) missingFields.push('Territory');
  if (!formData.configurationStore) missingFields.push('Store Configuration');
  if (!formData.seasonType) missingFields.push('Season Type');
  if (!formData.performanceMetric) missingFields.push('Performance Metric');
  if (!formData.event) missingFields.push('Event');
  if (!formData.evaluationPeriod.startDate) missingFields.push('Evaluation Start Date');
  if (!formData.evaluationPeriod.endDate) missingFields.push('Evaluation End Date');
  if (!formData.referencePeriod.startDate) missingFields.push('Reference Start Month');
  if (!formData.referencePeriod.endDate) missingFields.push('Reference End Month');
  // if (!files.value.cover.filename && !files.value.cover.fileObject) missingFields.push('Target Covers File');
  if (!isEvaluationPeriodValid.value) missingFields.push('Evaluation Period (invalid)');
  if (!isReferencePeriodValid.value) missingFields.push('Reference Period (invalid)');
  if (formData.configurationStore === 'Selected Stores' && (!formData.storeSelection || formData.storeSelection.length === 0)) {
    missingFields.push('Store Selection');
  }
  const hasFileErrors = Object.values(files.value).some(file => file?.error);
  if (hasFileErrors) missingFields.push('File Upload Error');
  console.log('Missing fields:', missingFields);
  if (missingFields.length > 0) {
    popupMessage.value = `Mandatory fields missing or invalid: \n- ${missingFields.join('\n- ')}`;
    showPopup.value = true;
    return false;
  }
  // If all fields are valid, proceed
  await handleContinue();
  return true;
}
// Computed property to check if form is complete
const isFormComplete = computed(() => {
  const requiredFields = [
    formData.scenarioName,
    formData.territory,
    formData.configurationStore,
    formData.seasonType,
    formData.performanceMetric,
    formData.event,
    formData.evaluationPeriod.startDate,
    formData.evaluationPeriod.endDate,
    formData.referencePeriod.startDate,
    formData.referencePeriod.endDate,
    // files.value.sqft.filename || files.value.sqft.fileObject,
    // files.value.cover.filename || files.value.cover.fileObject // require Cover file
  ]

  if (!isEvaluationPeriodValid.value || !isReferencePeriodValid.value) {
    return false
  }

  // Check if Selected Stores configuration requires store selection
  if (formData.configurationStore === 'Selected Stores' && (!formData.storeSelection || formData.storeSelection.length === 0)) {
    return false
  }

  const hasFileErrors = Object.values(files.value).some(file => file.error)

  if (hasFileErrors) {
    return false
  }

  // Check all required fields are filled
  const allFieldsFilled = requiredFields.every(field => field !== null && field !== '' && field !== undefined)

  // Check all required files are uploaded
  // const allFilesUploaded = Object.values(uploadedFiles.value).every(file => file !== null)

  return allFieldsFilled
})
// Fix date off-by-one issue for evaluationPeriod startDate and endDate
// Place this at the top-level of <script setup>
const formatDateForSave = (dateInput) => {
  if (!dateInput) return '';
  // If already in YYYY-MM-DD, return as is
  if (typeof dateInput === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateInput)) {
    return dateInput;
  }
  // If it's a Date object or string, parse and format as YYYY-MM-DD (local, not UTC)
  const date = new Date(dateInput);
  if (isNaN(date)) return '';
  // Adjust for timezone offset to get local date
  const localDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000);
  return localDate.toISOString().split('T')[0];
};

const handleContinue = async () => {
  isLoading.value = true
  const user_id = sessionStorage.getItem('user_id')
  const concpt = localStorage.getItem('concept') || 'hb'; // default to hb for now

  // Prepare payload object, map all fields you want to send
  const payload = {
    name: formData.scenarioName,
    season_type: formData.seasonType,
    user_id: user_id,  // or get dynamically
    eval_type: formData.configurationStore,
    event_name: formData.event,
    eval_start: formatDateForSave(formData.evaluationPeriod.startDate),
    eval_end: formatDateForSave(formData.evaluationPeriod.endDate),
    ref_start: formatMonth(formData.referencePeriod.startDate),
    ref_end: formatMonth(formData.referencePeriod.endDate),
    loc_cd: JSON.stringify(formData.storeSelection), // or array depending on backend
    CNCPT_NM: "hb",//update cncpt_nm to use concept from localstorage
    TERRITORY_NM: formData.territory, // use code or name accordingly
    metric: formData.performanceMetric,
    sqft_file_id: files.value.sqft?.fileId || null,
    sqft_filename: files.value.sqft?.filename || null,
    mdq_file_id: files.value.mdq?.fileId || null,
    mdq_filename: files.value.mdq?.filename || null,
    cover_file_id: files.value.cover?.fileId || null,
    cover_filename: files.value.cover?.filename || null,
    exclusion_file_id: files.value.exclusion?.fileId || null,
    exclusion_filename: files.value.exclusion?.filename || null,
    created_by: user_id,
    updated_by: user_id,
  }
  if (route.query.id) {
    payload.id = route.query.id;
  }

  try {
    // Call your API method here
    const response = await saveScenarioAPI(payload)
    console.log("Scenario saved:", response)

    if (response && response.scenario_details) {
      scenarioStore.setCurrentScenario(response.scenario_details)
      stepsStore.setScenarioData(response.scenario_details)
    }
    await fetchStoresByTerritory(formData.territory || formData.territory)
    if (formData.configurationStore === 'Selected Stores') {
      let old_locs = sessionStorage.getItem('loc_codes');
      if (old_locs && old_locs !== JSON.stringify(formData.storeSelection || [])) {
        sessionStorage.setItem('cluster_changed', true);
      }
      sessionStorage.setItem('loc_codes', JSON.stringify(formData.storeSelection || []));

    }
    sessionStorage.setItem('territory_name', formData.territory || formData.territory || '');
    sessionStorage.setItem('concept', 'hb');
    sessionStorage.setItem('performance_metric', formData.performanceMetric || '');
    // stepsStore.goToNextStep()
  } catch (error) {
    console.error("Failed to save scenario:", error)
    scenarioStore.setError(error.message || 'Failed to save scenario')
  } finally {
    isLoading.value = false
  }
}
const onConfigurationSelect = async (value) => {
  if (value === 'Selected Stores') {
    stepsStore.setStepVisible('Set Test & Control', true)
    if (formData.territory) {
      const stores = await fetchStoresByTerritory(formData.territory)
      storeOptions.value = stores
    }
  }
  if (value === 'Test & Control') {
    stepsStore.setStepVisible('Set Test & Control', false)
    storeOptions.value = []
    formData.storeSelection = []
  }
  if (value === null) {
    stepsStore.setStepVisible('Set Test & Control', true)
    stepsStore.setStepVisible('View Clusters', true)
    // Clear store options when switching away from Selected Stores
    storeOptions.value = []
    formData.storeSelection = []
  }
}
const handleFileUpload = (key, file) => {
  // Update the file object with the new file
  files.value[key] = {
    ...files.value[key],
    fileObject: file,
    filename: file.name
  }
}
const setStore = (value) => {
  sessionStorage.setItem('store_config', value)
  onConfigurationSelect(value)
}
// Handle upload result from FileUploadArea components
const handleUploadResult = (result) => {
  if (result.success && result.fileId && result.fileType) {
    // Update the combined file object
    files.value[result.fileType] = {
      ...files.value[result.fileType],
      fileId: result.fileId,
      filename: result.fileName,
      error: false
    }
  } else if (!result.success) {
    if (result.fileType) {
      files.value[result.fileType] = {
        ...files.value[result.fileType],
        fileId: null,
        filename: null,
        error: result.error !== undefined ? result.error : true
      }
    }
  }
}


const fetchStoresByTerritory = async (territoryCode) => {

  loadingStores.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    const response = await axios.post('/scenario/select_store_dropdown/', {
      "concept": "hb",
      "territory": territoryCode
    });
    storeOptions.value = response.data;

    return response.data

  } catch (error) {
    console.error('Error fetching stores:', error)
    return []
  } finally {
    loadingStores.value = false
  }
}
const getTerritoryData = async () => {
  loadingStores.value = true
  try {
    const concept = localStorage.getItem('concept')
    const response = await axios.get('/scenario/getTerritoryData/', {
      params: { concept }
    })

    territories.value = response.data.territories.map(t => ({
      value: t.trty_short_name,
      label: t.trty_display_name,
      image: `${baseImgUrl}/${t.trty_short_name.toLowerCase()}.svg`
    }))
  } catch (error) {
    console.error('Error fetching territories:', error)
    territories.value = []
  } finally {
    loadingStores.value = false
  }
}

const onTerritorySelect = async (territory) => {
  if (territory && formData.configurationStore === 'Selected Stores') {
    const stores = await fetchStoresByTerritory(territory)
    storeOptions.value = stores
    // Clear previous selections when territory changes
    formData.storeSelection = []
  }
}


const onSeasonTypeSelect = (val) => {

  if (val === 'in season') {
    // stepsStore.setStepVisible('Range', false)
    stepsStore.setStepVisible('Optimize', false)
  }
  if (val === 'pre season') {
    stepsStore.setStepVisible('Range', true)
    stepsStore.setStepVisible('Optimize', false)
  }
  if (val === null) {
    stepsStore.setStepVisible('Range', true)
    stepsStore.setStepVisible('Optimize', true)
  }
}

const setDragActive = (key) => {
  dragActive.value = key
}

provide('files', files)
provide('dragActive', dragActive)
provide('handleFileUpload', handleFileUpload)
provide('setDragActive', setDragActive)

function getCurrentMonthYear() {
  const now = new Date();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 01 - 12
  const year = now.getFullYear();
  return `${year}-${month}`;
}

const formData = reactive({
  scenario_id: null,
  configurationStore: null,
  territory: null,
  scenarioName: '',
  seasonPreference: 'in-season',
  seasonType: null,
  performanceMetric: null,
  storeSelection: [],
  event: null,
  evaluationPeriod: {
    startDate: '',
    endDate: ''
  },
  referencePeriod: {
    startDate: '',
    endDate: ''
  },
})


watch(
  () => [formData.configurationStore, formData.storeSelection],
  ([config, selection]) => {
    if (
      (config === 'Selected Stores' && selection.length > 0) ||
      config === 'Test & Control'
    ) {
      getStoreAvailability()
    }
  },
  { deep: true }
)




const fillFormFromStore = async (storedData) => {
  if (storedData && Object.keys(storedData).length > 0) {
    formData.scenario_id = storedData.id ?? formData.scenario_id
    formData.configurationStore = storedData.eval_type ?? formData.configurationStore
    await getTerritoryData()
    formData.territory = storedData.TERRITORY_NM ?? storedData.territory_name ?? formData.territory
    formData.scenarioName = storedData.name ?? formData.scenarioName
    formData.seasonType = storedData.season_type ?? formData.seasonType
    formData.performanceMetric = storedData.metric ?? formData.performanceMetric
    if (formData.seasonType === 'in season') {
      formData.event = storedData.name ?? ''
    } else {
      formData.event = storedData.event_name ?? formData.event
    }
    formData.evaluationPeriod.startDate = formatDate(storedData.eval_start) || formData.evaluationPeriod.startDate
    formData.evaluationPeriod.endDate = formatDate(storedData.eval_end) || formData.evaluationPeriod.endDate
    formData.referencePeriod.startDate = formatMonth(storedData.ref_start) || formData.referencePeriod.startDate
    formData.referencePeriod.endDate = formatMonth(storedData.ref_end) || formData.referencePeriod.endDate
    sessionStorage.setItem('store_config', formData.configurationStore)

    if (formData.configurationStore === 'Selected Stores' && formData.territory) {
      const storeList = await fetchStoresByTerritory(formData.territory || formData.territory)
      storeOptions.value = storeList
    }

    let selected_store = storedData?.loc_cd || storedData?.location_codes || []
    try {
      const parsedSelectedStore = JSON.parse(selected_store)
      formData.storeSelection = parsedSelectedStore
    } catch (e) {
      formData.storeSelection = []
    }
    // Populate the combined files object from store data
    const fileTypes = ['sqft', 'mdq', 'cover', 'exclusion']
    console.log("storedData", storedData)
    fileTypes.forEach(type => {
      const filenameKey = `${type}_filename`
      const fileIdKey = `${type}_file_id`

      if (storedData[filenameKey] || storedData[fileIdKey]) {
        files.value[type] = {
          filename: storedData[filenameKey] || null,
          fileId: storedData[fileIdKey] || null,
          fileObject: null // No actual file object when loading from store
        }
      }
    })
  }
}

const validateEvaluationPeriod = () => {
  const { startDate, endDate } = formData.evaluationPeriod
  const today = new Date().toISOString().split('T')[0]


  if (startDate && endDate && endDate < startDate) {
    popupMessage.value = 'Evaluation End Date should not be earlier than Start Date.'
    showPopup.value = true
    isEvaluationPeriodValid.value = false
    return
  }
  isEvaluationPeriodValid.value = true
}

const validateReferencePeriod = () => {
  const { startDate, endDate } = formData.referencePeriod
  reRunOptimizer()
  if (startDate && endDate && endDate < startDate) {
    popupMessage.value = 'Reference Period End Month should not be earlier than Start Month.'
    showPopup.value = true
    isReferencePeriodValid.value = false
    return
  }
  isReferencePeriodValid.value = true
}


onMounted(() => {
  fillFormFromStore(stepsStore.getScenarioData)
  getTerritoryData()

  // Add event listeners to recalculate tooltip position
  window.addEventListener('resize', calculateTooltipPosition)
  window.addEventListener('scroll', handleScroll, true) // Use capture to catch all scroll events
  document.addEventListener('click', handleClickOutside)
})

// Clean up event listeners on unmount
onUnmounted(() => {
  window.removeEventListener('resize', calculateTooltipPosition)
  window.removeEventListener('scroll', handleScroll, true)
  document.removeEventListener('click', handleClickOutside)
})

const formatRefMonth = (yyyymm) => {
  const str_yyyymm = yyyymm.toString()
  return `${str_yyyymm.slice(0, 4)}-${str_yyyymm.slice(4, 6)}`
}
const handleTerritoryChange = () => {
  formData.storeSelection = [];
  formData.referencePeriod.startDate = ''
  formData.referencePeriod.endDate = ''
  // Disable and hide all steps except Inputs
  if (typeof stepsStore !== 'undefined' && stepsStore.steps) {
    stepsStore.steps.forEach((step, index) => {
      console.log(step.name)
      if (step.name === 'Inputs') {
        step.disabled = false;
        step.hidden = false;
      } else {
        step.disabled = true;
        step.hidden = false;
      }
    });
    stepsStore.currentStep = 0;
  }
  sessionStorage.removeItem('loc_codes')
  sessionStorage.removeItem('testControlMappings')
  reRunOptimizer()
}
const reRunOptimizer = () => {
  setOptimizerRunStatus({
    scenario_id: sessionStorage.getItem('scenario_id'),
    run_optimizer: 0,
    run_performance: 0,
    run_range: 0
  })
}
// Watch for territory changes and fetch stores
watch(
  () => formData.territory,
  async (newTerritory) => {
    // Clear selected stores when territory changes
    const code = newTerritory?.code || newTerritory || ''
    const concept = localStorage.getItem('concept')
    if (code) {
      await fetchStoresByTerritory(code)
      const response = await fetchRefPeriod(concept, code)
      const newStart = formatRefMonth(response.start_month)
      const newEnd = formatRefMonth(response.end_month)

      endMonth.value = newEnd
      const currentRefStart = formData.referencePeriod.startDate
      const currentRefEnd = formData.referencePeriod.endDate

      // Fix invalid start month
      if (!currentRefStart || currentRefStart < newStart || currentRefStart > newEnd) {
        formData.referencePeriod.startDate = newStart
      }

      // Fix invalid end month
      if (!currentRefEnd || currentRefEnd < newStart || currentRefEnd > newEnd) {
        formData.referencePeriod.endDate = newEnd
      }
    }
  }
)

watch(() => stepsStore.getScenarioData, (newData) => {
  fillFormFromStore(newData)
}, { deep: true, immediate: false })

const formatDate = (dateString) => {
  if (!dateString) return '';
  // If already in YYYY-MM-DD, return as is
  if (typeof dateString === 'string' && /^\d{4}-\d{2}-\d{2}$/.test(dateString)) {
    return dateString;
  }
  const date = new Date(dateString);
  if (isNaN(date)) return '';
  // Adjust for timezone offset to get local date
  const localDate = new Date(date.getTime() - date.getTimezoneOffset() * 60000);
  return localDate.toISOString().split('T')[0];
}
const formatMonth = (dateInput) => {
  if (!dateInput) return ''
  
  // If it's already in YYYY-MM format, return as is
  if (typeof dateInput === 'string' && /^\d{4}-\d{2}$/.test(dateInput)) {
    return dateInput
  }

  // Handle date object or date string
  const date = new Date(dateInput)
  if (isNaN(date)) return ''
  
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}` // "YYYY-MM"
}

const formatAvailabilityMonth = (monthValue) => {
  if(!monthValue) return ''

  const year = parseInt(monthValue.slice(0, 4), 10);
  const month = parseInt(monthValue.slice(4, 6), 10) - 1;
  
  const date = new Date(year, month);
  return date.toLocaleString('en-US', { month: 'short', year: 'numeric' });
}

const storeDropdownOpen = ref(false)
const storeOptions = ref([])
const configurationStoreOptions = ['Selected Stores','Test & Control']
const territories = ref([])
const seasonTypeOptions = [
  { value: 'in season', label: 'In-Season' },
  { value: 'pre season', label: 'Pre-Season' }
]
const performanceOptions = [
  { value: 'REVENUE', label: 'Revenue' },
  { value: 'GMV', label: 'Margin' }
]

const toggleStoreSelection = (store) => {
  const storeCode = store.code; // extract the code only
  const index = formData.storeSelection.indexOf(storeCode);
  if (index === -1) {
    formData.storeSelection.push(storeCode);  // push only the code
  } else {
    formData.storeSelection.splice(index, 1); // remove by code
  }
};

// const getTerritoryName = (code) => {
//   const found = locations.find(loc => loc.code === code)
//   return found ? found.label : 'Unknown'
// }

const onTerritoryChange = async (selectedTerritory) => {
  console.log('Territory selected:', selectedTerritory)
  const concept = localStorage.getItem('concept')
  // Example: Fetch store list based on selected territory and concept
  if (selectedTerritory) {
    const stores = await fetchStores(concept, selectedTerritory.code)
    console.log("stores value is ", stores)
    storeOptions.value = stores.map(store => ({
      label: store.loc_nm,
      code: store.loc_cd
    }))
  }
}




const generateAnalysis = () => {
  console.log('Generating analysis with:', JSON.stringify(formData, null, 2))
  // You can replace this with actual logic

}


const removeStore = (store) => {
  formData.storeSelection = formData.storeSelection.filter(s => s !== store)
}
const oldScenarioName = ref(formData.scenarioName)
watch(
  () => [formData.seasonType, formData.scenarioName],
  ([newSeasonType, newScenarioName]) => {
    if (newSeasonType === 'in season') {
      // Only auto-set if event field is empty or equal to old scenario name
      if (!formData.event || formData.event === oldScenarioName.value) {
        formData.event = newScenarioName
      }
    }
    oldScenarioName.value = newScenarioName
  },
  { immediate: true }
)

defineExpose({ beforeLeave });

</script>

<style scoped>.rotate-180 {
  transform: rotate(180deg);
}
::placeholder {
  color: #d1d5db;
  font-size: 14px;
}

.p-datepicker-calendar-container{
  background-color: white !important;
}

:deep(.datepicker-custom) {
  width: 100%;
}

:deep(.datepicker-custom .p-calendar) {
  position: relative;
  width: 100%;
}

:deep(.datepicker-custom .p-inputtext) {
  height: 2.5rem;
  background-color: white !important;
  border: 1px solid #E5E7EB;
  border-radius: 0.5rem;
  transition: all 0.3s;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  width: 100%;
}

:deep(.datepicker-custom .p-datepicker) {
  position: absolute;
  width: 320px !important; /* Fixed width */
  margin-top: 0.5rem;
  border-radius: 0.75rem;
  padding: 0.75rem;
  background-color: white !important;
  border: 1px solid #E5E7EB;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  right: 0;
  z-index: 1000;
}

:deep(.datepicker-custom.p-calendar-open .p-inputtext) {
  border-color: var(--primary-color);
  background-color: white !important;
}

:deep(.datepicker-custom .p-datepicker-header) {
  background-color: white;
  border-bottom: 1px solid #E5E7EB;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
}

:deep(.datepicker-custom .p-datepicker-calendar) {
  margin: 0;
  background-color: white;
}

:deep(.datepicker-custom .p-datepicker-calendar th) {
  padding: 0.5rem;
  color: #6B7280;
  font-weight: 600;
}

:deep(.datepicker-custom .p-datepicker-calendar td) {
  padding: 0.25rem;
}

:deep(.datepicker-custom .p-datepicker-calendar .p-highlight) {
  background-color: var(--primary-color);
  color: white;
  border-radius: 0.375rem;
}

:deep(.datepicker-custom .p-datepicker-calendar td:not(.p-disabled):hover) {
  background-color: #F3F4F6;
  border-radius: 0.375rem;
  cursor: pointer;
}

:deep(.datepicker-custom .p-datepicker-calendar td span) {
  width: 2rem;
  height: 2rem;
  border-radius: 0.375rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
.datepicker-custom .p-inputtext {
  padding-right: 2.5rem; /* give space for icon inside */
}
.date-panel-custom {
  font-size: 0.8rem;   /* smaller text */
  padding: 0.25rem;    /* tighter spacing */
}
</style>