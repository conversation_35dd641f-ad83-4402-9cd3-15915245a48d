<template>
  <div class="p-1 space-y-2">
    <!-- Filters Section -->
    <div class="bg-white rounded-lg shadow-sm border px-4 py-2">
      <div class="flex gap-3">
        <div class="w-full sm:w-1/5">
          <label class="text-sm font-semibold text-gray-700 mb-1 block"
            >Cluster</label
          >
          <DynamicFilter
            v-model="selectedCluster"
            :multiselect="false"
            label="Cluster"
            placeholder="Select Cluster"
            :options="clusterOptions || []"
            value-key="code"
            label-key="name"
            :searchable="false"
            variant="outline"
            size="sm"
            :close-on-select="true"
            @change="handelClusterChange()"
          />
        </div>
        <div class="w-full sm:w-1/4">
          <label class="text-sm font-semibold text-gray-700 mb-1 block"
            >Store</label
          >
          <DynamicFilter
            v-model="selectedLocation"
            :multiselect="true"
            label="Store"
            placeholder="Select Stores"
            :options="locationOptions || []"
            value-key="code"
            label-key="name"
            :searchable="true"
            variant="outline"
            size="sm"
          />
        </div>
        <div class="w-full sm:w-1/4">
          <label class="text-sm font-semibold text-gray-700 mb-1 block"
            >Subclass</label
          >
          <DynamicFilter
            v-model="selectedSubclass"
            :multiselect="false"
            label="Subclass"
            placeholder="Select Subclass"
            :options="subclassOptions || []"
            value-key="code"
            label-key="name"
            :searchable="true"
            :close-on-select="true"
            variant="outline"
            size="sm"
          />
        </div>
        <div class="w-full sm:w-1/3 flex items-end gap-2">
          <button
            @click="resetFilters"
            class="px-4 py-2 h-10 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-secondary"
          >
            Clear All
          </button>
          <button
            @click="applyFilters"
            class="px-4 py-2 h-10 text-sm font-medium border border-transparent rounded-md focus:outline-none focus:ring-2 bg-tertiary hover:bg-tertiary text-tx-primary focus:ring-secondary"
          >
            Apply Filters
          </button>
        </div>
      </div>
    </div>

    <!-- Chart Section -->
    <div class="bg-white rounded-lg shadow-sm border px-6 py-2">
      <!-- Chart Header -->
      <div class="mb-2">
        <h3 class="text-sm font-bold text-gray-800 mb-2">
          {{ chartTitle }}
        </h3>
        <!-- <div class="text-sm text-gray-600">
          Revenue per Day vs Linear Meter Analysis
        </div> -->
      </div>

      <!-- Chart Options -->
      <div class="flex items-center justify-between mb-2 flex-wrap gap-4">
        <!-- Legend with counts -->
        <div class="flex gap-6 flex-wrap">
          <div class="flex items-center gap-2">
            <div
              class="w-2 h-2 rounded-full"
              style="background: linear-gradient(135deg, #10b981, #059669)"
            ></div>
            <span class="text-xs text-gray-700 font-medium"
              >Non Outlier ({{ nonOutlierCount }})</span
            >
          </div>
          <div class="flex items-center gap-2">
            <div
              class="w-2 h-2 rounded-full"
              style="background: linear-gradient(135deg, #f59e0b, #d97706)"
            ></div>
            <span class="text-xs text-gray-700 font-medium"
              >Minor Outlier ({{ minorOutlierCount }})</span
            >
          </div>
          <div class="flex items-center gap-2">
            <div
              class="w-2 h-2 rounded-full"
              style="background: linear-gradient(135deg, #ef4444, #dc2626)"
            ></div>
            <span class="text-xs text-gray-700 font-medium"
              >Major Outlier ({{ majorOutlierCount }})</span
            >
          </div>
        </div>
      </div>

      <!-- Chart Container -->
      <div class="relative">
        <div
          ref="chartContainer"
          style="width: 100%; height: 300px"
          class="rounded-lg border"
        ></div>
        <div
          v-if="!fetchedRows.length"
          class="absolute inset-0 flex items-center justify-center bg-gray-50 rounded-lg"
        >
          <div class="text-gray-500 text-center">
            <div class="text-4xl mb-4">📊</div>
            <div class="text-lg font-medium">No data available</div>
            <div class="text-sm">Please adjust your filters to see data</div>
          </div>
        </div>
      </div>

      <!-- Data Summary Cards -->
      <div
        v-if="fetchedRows.length"
        class="mt-2 grid grid-cols-4 md:grid-cols-4 gap-2"
      >
        <div
          class="bg-gradient-to-r from-blue-50 to-blue-100 p-2 rounded-md border border-blue-200"
        >
          <div class="flex justify-between text-sm text-blue-600 font-medium">
            <span>Total Records:</span>
            <span class="font-bold text-blue-800">{{
              fetchedRows.length
            }}</span>
          </div>
        </div>
        <div
          class="bg-gradient-to-r from-green-50 to-green-100 p-2 rounded-md border border-green-200"
        >
          <div class="flex justify-between text-sm text-secondary font-medium">
            <span v-if="performance_metric==='GMV'">Avg GMV/Day:</span>
            <span v-else>Avg Revenue/Day:</span>
            <span class="font-bold text-green-800">{{ averageRevenue }}</span>
          </div>
        </div>
        <div
          class="bg-gradient-to-r from-purple-50 to-purple-100 p-2 rounded-md border border-purple-200"
        >
          <div class="flex justify-between text-sm text-purple-600 font-medium">
            <span>Avg Linear Meter:</span>
            <span class="font-bold text-purple-800">{{
              averageLinearMeter
            }}</span>
          </div>
        </div>
        <div
          class="bg-gradient-to-r from-pink-50 to-pink-100 p-2 rounded-md border border-pink-200"
        >
          <div class="flex justify-between text-sm text-pink-600 font-medium">
            <span>Outlier Rate:</span>
            <span class="font-bold text-pink-800">{{ outlierRate }}%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    v-if="isTableLoading"
    class="fixed inset-0 bg-black bg-opacity-30 flex items-center justify-center z-50"
  >
          <div class="bg-white rounded-lg p-2 flex items-center justify-center">
            <div class="tw-loader"></div> 
            <div class="mr-3">{{ loadingMessage }}</div>
          </div>
  </div>
</template>

<script setup>
import {
  ref,
  watch,
  onMounted,
  computed,
  nextTick,
  onBeforeUnmount,
} from "vue";
import DynamicFilter from "./common/DynamicFilter.vue";
import * as echarts from "echarts";
import { fetchOutlierVisualizationData } from "../services/api.js";

// Props
const props = defineProps({
  originalRows: {
    type: Array,
    default: () => [],
  },
});

// Reactive variables
const selectedSubclass = ref(null);
const selectedLocation = ref([]);
const selectedCluster = ref(null);
const chartContainer = ref(null);
const fetchedRows = ref([]);
let chartInstance = null;

const nonOutlierCount = ref(0);
const minorOutlierCount = ref(0);
const majorOutlierCount = ref(0);
const averageRevenue = ref("0");
const averageLinearMeter = ref("0");
const selectedStoresCount = ref(0);
const outlierRate = ref("0");
const isTableLoading = ref(false);
const loadingMessage = ref("Loading data...");
const chartTitle = ref("Revenue per Day vs Linear Meter");
let performance_metric = sessionStorage.getItem("performance_metric") || "GMV";

// Chart colors for different outlier types
const OUTLIER_CHART_COLORS = {
  NON_OUTLIER: {
    color: ["#10b981", "#059669"],
    symbolSize: 8,
  },
  MINOR_OUTLIER: {
    color: ["#f59e0b", "#d97706"],
    symbolSize: 10,
  },
  MAJOR_OUTLIER: {
    color: ["#ef4444", "#dc2626"],
    symbolSize: 12,
  },
};

// Computed options for dropdowns
const subclassOptions = computed(() => {
  const uniqueSubclasses = [
    ...new Set(props.originalRows.map((d) => d.sub_clss_nm)),
  ];
  return uniqueSubclasses.map((d) => ({
    code: d,
    name: d,
  }));
});

const handelClusterChange = () => {
  selectedLocation.value = [];
  selectedSubclass.value = [];
};

const handelStoreChange = () => {
  selectedSubclass.value = [];
};

const locationOptions = computed(() => {
  let filteredRows = props.originalRows;

  // Filter by cluster if selected
  if (selectedCluster.value !== null) {
    filteredRows = filteredRows.filter(
      (d) => d.cluster_num === selectedCluster.value
    );
  }

  // Filter by subclass if selected
  if (selectedSubclass.value && selectedSubclass.value.length > 0) {
    filteredRows = filteredRows.filter(
      (d) => d.sub_clss_nm === selectedSubclass.value
    );
  }
  const uniqueLocations = [
    ...new Map(filteredRows.map((d) => [d.loc_cd, d])).values(),
  ];

  return uniqueLocations.map((d) => ({
    code: d.loc_cd,
    name: `${d.loc_cd} - ${d.loc_nm}`,
  }));
});

const clusterOptions = computed(() => {
  const uniqueClusters = [
    ...new Set(props.originalRows.map((row) => row.cluster_num)),
  ].sort((a, b) => a - b);
  console.log(uniqueClusters, props.originalRows);
  return uniqueClusters.map((c) => ({
    code: c,
    name: `Cluster ${c}`,
  }));
});

async function applyFilters() {
  isTableLoading.value = true;
  loadingMessage.value = "Applying filters...";
  try {
    const params = {
      loc_cd: selectedLocation.value,
      cluster: selectedCluster.value,
      sub_class: selectedSubclass.value,
    };
    const response = await fetchOutlierVisualizationData(params);
    fetchedRows.value = response.data;
    calculateStats();
    getChartTitle();
    await nextTick();
    initChart();
    updateChart();
  } catch (err) {
    console.error("Error fetching data:", err);
  } finally {
    isTableLoading.value = false;
  }
}

function calculateStats() {
  const filtered = fetchedRows.value;

  nonOutlierCount.value = filtered.filter(
    (d) => d.outlier_status_final === "NON_OUTLIER"
  ).length;
  minorOutlierCount.value = filtered.filter(
    (d) => d.outlier_status_final === "MINOR_OUTLIER"
  ).length;
  majorOutlierCount.value = filtered.filter(
    (d) => d.outlier_status_final === "MAJOR_OUTLIER"
  ).length;

  const totalRecords = filtered.length;

  if (totalRecords > 0) {
    averageRevenue.value = (
      filtered.reduce((sum, d) => sum + d.per_day, 0) / totalRecords
    ).toFixed(1);
    averageLinearMeter.value = (
      filtered.reduce((sum, d) => sum + d.total_lm, 0) / totalRecords
    ).toFixed(1);

    const outlierCount = majorOutlierCount.value + minorOutlierCount.value
    outlierRate.value = ((outlierCount / totalRecords) * 100).toFixed(1);
  } else {
    averageRevenue.value = "0";
    averageLinearMeter.value = "0";
    outlierRate.value = "0";
  }

  if (!selectedLocation.value || selectedLocation.value.length === 0) {
    selectedStoresCount.value = new Set(filtered.map((d) => d.storeId)).size;
  } else {
    selectedStoresCount.value = selectedLocation.value.length;
  }
}

// Filtered data based on selections
function getFilteredData() {
  let filtered = fetchedRows.value;

  if (selectedCluster.value !== null) {
    filtered = filtered.filter(
      (d) => d.new_cluster_num === selectedCluster.value
    );
  }

  if (selectedSubclass.value) {
    filtered = filtered.filter((d) => d.sub_clss_nm === selectedSubclass.value);
  }

  if (selectedLocation.value && selectedLocation.value.length > 0) {
    filtered = filtered.filter((d) =>
      selectedLocation.value.includes(d.loc_cd)
    );
  }

  return filtered;
}

// Chart title
function getChartTitle() {
  let title = "";
  if (performance_metric == "GMV") {
    title = "GMV per Day vs Linear Meter";
  } else {
    title = "Revenue per Day vs Linear Meter";
  }
  if (selectedSubclass.value) {
    title = `${selectedSubclass.value} - ${title}`;
  }

  if (selectedCluster.value !== null) {
    title = `Cluster ${selectedCluster.value} - ${title}`;
  }

  if (selectedLocation.value && selectedLocation.value.length === 1) {
    const store = fetchedRows.value.find(
      (d) => d.loc_cd === selectedLocation.value[0]
    );
    if (store) {
      title = `${store.loc_cd} (${store.loc_nm}) - ${title}`;
    }
  } else if (selectedLocation.value && selectedLocation.value.length > 1) {
    title = `${selectedLocation.value.length} Stores - ${title}`;
  }
  chartTitle.value = title;
  return title;
}

// ECharts functions
function initChart() {
  if (!chartContainer.value) return;

  if (chartInstance) {
    chartInstance.dispose();
  }

  chartInstance = echarts.init(chartContainer.value, null, {
    renderer: "canvas",
    useDirtyRect: false,
  });

  // Set up resize handler
  window.addEventListener("resize", () => {
    if (chartInstance) {
      chartInstance.resize();
    }
  });
}

function updateChart() {
  if (!chartInstance || !fetchedRows.value.length) return;

  // Prepare data for each outlier type
  const seriesData = {
    NON_OUTLIER: [],
    MINOR_OUTLIER: [],
    MAJOR_OUTLIER: [],
  };

  fetchedRows.value.forEach((d) => {
    const dataPoint = {
      value: [d.total_lm, d.per_day],
      itemStyle: {
        color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
          {
            offset: 0,
            color: OUTLIER_CHART_COLORS[d.outlier_status_final].color[0],
          },
          {
            offset: 1,
            color: OUTLIER_CHART_COLORS[d.outlier_status_final].color[1],
          },
        ]),
      },
      symbolSize: OUTLIER_CHART_COLORS[d.outlier_status_final].symbolSize,
      store: `${d.loc_cd} - ${d.loc_nm}`,
      month: formatMonth(d.month),
      status: d.outlier_status_final,
      lmContribution: d.lm_contribution_in_store,
      suggestedLm: d.suggested_total_lm,
    };
    seriesData[d.outlier_status_final].push(dataPoint);
  });

  const option = {
    backgroundColor: "#ffffff",
    grid: {
      left: "12%",
      right: "10%",
      bottom: "15%",
      top: "10%",
      containLabel: true,
    },
    xAxis: {
      type: "value",
      name: "Linear Meter (LM)",
      nameLocation: "middle",
      nameGap: 30,
      nameTextStyle: {
        fontSize: 14,
        fontWeight: "bold",
        color: "#374151",
      },
      axisLine: {
        lineStyle: {
          color: "#d1d5db",
          width: 2,
        },
      },
      axisLabel: {
        color: "#6b7280",
        fontSize: 12,
      },
      splitLine: {
        lineStyle: {
          color: "#f3f4f6",
          type: "dashed",
        },
      },
    },
    yAxis: {
      type: "value",
      name: performance_metric === "GMV" ? "GMV per Day" : "Revenue per Day",
      nameLocation: "middle",
      nameGap: 50,
      nameTextStyle: {
        fontSize: 14,
        fontWeight: "bold",
        color: "#374151",
      },
      axisLine: {
        lineStyle: {
          color: "#d1d5db",
          width: 2,
        },
      },
      axisLabel: {
        color: "#6b7280",
        fontSize: 12,
        formatter: function (value) {
          return value.toFixed(0);
        },
      },
      splitLine: {
        lineStyle: {
          color: "#f3f4f6",
          type: "dashed",
        },
      },
    },
    tooltip: {
      trigger: "item",
      backgroundColor: "rgba(255, 255, 255, 0.95)",
      borderColor: "#e5e7eb",
      borderWidth: 1,
      textStyle: {
        color: "#374151",
        fontSize: 12,
      },
      formatter: function (params) {
        const data = params.data;
        return `
          <div style="padding: 8px;">
            <div style="font-weight: bold; margin-bottom: 8px; color: #1f2937;">${
              data.store
            }</div>
            <div style="display: flex; flex-direction: column; gap: 4px;">
              <div><span style="color: #6b7280;">Linear Meter:</span> <strong>${data.value[0].toFixed(
                1
              )}</strong></div>
              <div>
                        <span style="color: #6b7280;">
          ${performance_metric === "GMV" ? "GMV/Day" : "Revenue/Day"}:
        </span> 
                <strong>${data.value[1].toFixed(
                1
              )}</strong></div>
              <div><span style="color: #6b7280;">Month:</span> <strong>${
                data.month
              }</strong></div>
              <div><span style="color: #6b7280;">Status:</span> <strong style="color: ${
                OUTLIER_CHART_COLORS[data.status].color[0]
              };">${data.status.replace("_", " ")}</strong></div>
              <div><span style="color: #6b7280;">LM Contribution:</span> <strong>${
                data.lmContribution
              }</strong></div>
              <div><span style="color: #6b7280;">Suggested LM:</span> <strong>${
                data.suggestedLm
              }</strong></div>
            </div>
          </div>
        `;
      },
    },
    toolbox: {
      right: 20,
      top: 20,
      feature: {
        dataZoom: {
          yAxisIndex: "none",
          title: {
            zoom: "Zoom",
            back: "Reset Zoom",
          },
        },
        saveAsImage: {
          title: "Save as Image",
          pixelRatio: 2,
        },
      },
      iconStyle: {
        borderColor: "#6b7280",
      },
    },
    dataZoom: [
      {
        type: "inside",
        xAxisIndex: 0,
        filterMode: "none",
      },
      {
        type: "inside",
        yAxisIndex: 0,
        filterMode: "none",
      },
    ],
    series: [
      {
        name: "Non Outlier",
        type: "scatter",
        data: seriesData.NON_OUTLIER,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(16, 185, 129, 0.5)",
          },
        },
      },
      {
        name: "Minor Outlier",
        type: "scatter",
        data: seriesData.MINOR_OUTLIER,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(245, 158, 11, 0.5)",
          },
        },
      },
      {
        name: "Major Outlier",
        type: "scatter",
        data: seriesData.MAJOR_OUTLIER,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(239, 68, 68, 0.5)",
          },
        },
      },
    ],
    animationDuration: 1000,
    animationEasing: "cubicOut",
  };

  chartInstance.setOption(option, true);
}

function resetZoom() {
  if (chartInstance) {
    chartInstance.dispatchAction({
      type: "dataZoom",
      start: 0,
      end: 100,
    });
  }
}

function downloadChart() {
  if (chartInstance) {
    const url = chartInstance.getDataURL({
      pixelRatio: 2,
      backgroundColor: "#ffffff",
    });
    const link = document.createElement("a");
    link.download = `outlier-analysis-${
      new Date().toISOString().split("T")[0]
    }.png`;
    link.href = url;
    link.click();
  }
}

function formatMonth(monthStr) {
  const year = +monthStr.slice(0, 4);
  const month = +monthStr.slice(4, 6) - 1;
  const date = new Date(year, month);
  return date.toLocaleDateString("en-US", {
    month: "short",
    year: "numeric",
  });
}

function resetFilters() {
  selectedLocation.value = [];
  selectedCluster.value = [];
  selectedSubclass.value = [];
}

// Lifecycle hooks
onMounted(async () => {
  isTableLoading.value = true;
  loadingMessage.value = "Initializing chart...";
  try {
    if (clusterOptions.value.length > 0 && selectedCluster.value === null) {
      selectedCluster.value = clusterOptions.value[0].code;
    }

    if (subclassOptions.value.length > 0 && !selectedSubclass.value) {
      selectedSubclass.value = subclassOptions.value[0].code;
    }

    const params = {
      loc_cd: selectedLocation.value,
      cluster: selectedCluster.value,
      sub_class: selectedSubclass.value,
    };

    const response = await fetchOutlierVisualizationData(params);
    fetchedRows.value = response.data;
    getFilteredData();
    calculateStats();
    getChartTitle();
    await nextTick();
    initChart();
    updateChart();
  } catch (err) {
    console.error("Error initializing chart:", err);
  } finally {
    isTableLoading.value = false;
  }
});

// Cleanup
onBeforeUnmount(() => {
  if (chartInstance) {
    chartInstance.dispose();
  }
  window.removeEventListener("resize", () => {});
});
</script>

<style scoped>
/* Custom multiselect styles */
:deep(.multiselect) {
  min-height: 38px;
}

:deep(.multiselect__single) {
  font-size: 14px;
  margin-bottom: 0;
  padding: 0 8px;
}

:deep(.multiselect__tags) {
  min-height: 38px;
  padding: 6px 8px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
}

:deep(.multiselect__placeholder) {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 0;
  padding-top: 0;
}

:deep(.multiselect__tag) {
  background: #3b82f6;
  color: white;
  font-size: 12px;
}

:deep(.multiselect__option--highlight) {
  background: #3b82f6;
  color: white;
}

:deep(.multiselect__option--selected) {
  background: #1e40af;
  color: white;
}

/* Custom button styles */
button {
  transition: all 0.2s ease;
}

button:hover {
  transform: translateY(-1px);
}

/* Chart container styling */
.chart-container {
  border-radius: 8px;
  overflow: hidden;
}
</style>
